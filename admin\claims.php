<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle claim status updates
if(isset($_POST['update_claim_status'])) {
    $claim_id = $_POST['claim_id'];
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes']);
    
    try {
        $stmt = $conn->prepare("UPDATE claims SET status = :status, notes = :notes, processed_by = :processed_by, processed_at = NOW() WHERE claim_id = :claim_id");
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':processed_by', $_SESSION['user_id']);
        $stmt->bindParam(':claim_id', $claim_id);
        
        if($stmt->execute()) {
            $success_message = "Claim status updated successfully!";
        } else {
            $error_message = "Error updating claim status.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all claims with customer and policy info
$query = "SELECT c.*, cp.policy_number, p.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email,
                 CONCAT(processor.first_name, ' ', processor.last_name) as processed_by_name
          FROM claims c
          JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          LEFT JOIN users processor ON c.processed_by = processor.user_id
          ORDER BY c.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$claims = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Claims";
$page_description = "View and process all insurance claims.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 12px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-under-review { background-color: #cce5ff; color: #004085; }
        .status-approved { background-color: #d4edda; color: #155724; }
        .status-rejected { background-color: #f8d7da; color: #721c24; }
        .status-processed { background-color: #d1ecf1; color: #0c5460; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #dc3545; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .search-box { margin-bottom: 20px; }
        .search-box input, .search-box select { padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #dc3545; }
        .stat-number { font-size: 24px; font-weight: bold; color: #dc3545; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <div class="content">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- Claims Statistics -->
            <div class="stats-grid">
                <?php
                $total_claims = count($claims);
                $pending_claims = count(array_filter($claims, function($c) { return $c['status'] == 'Pending'; }));
                $approved_claims = count(array_filter($claims, function($c) { return $c['status'] == 'Approved'; }));
                $total_amount = array_sum(array_column($claims, 'claim_amount'));
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_claims; ?></div>
                    <div class="stat-label">Total Claims</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $pending_claims; ?></div>
                    <div class="stat-label">Pending Review</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $approved_claims; ?></div>
                    <div class="stat-label">Approved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSH <?php echo number_format($total_amount, 0); ?></div>
                    <div class="stat-label">Total Amount</div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>All Claims (<?php echo count($claims); ?>)</h2>
            </div>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Search claims..." onkeyup="searchClaims()">
                <select id="statusFilter" onchange="filterByStatus()">
                    <option value="">All Statuses</option>
                    <option value="Pending">Pending</option>
                    <option value="Under Review">Under Review</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="Processed">Processed</option>
                </select>
            </div>

            <table class="table" id="claimsTable">
                <thead>
                    <tr>
                        <th>Claim #</th>
                        <th>Customer</th>
                        <th>Policy</th>
                        <th>Incident Date</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Submitted</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($claims as $claim): ?>
                    <tr>
                        <td><strong><?php echo htmlspecialchars($claim['claim_number']); ?></strong></td>
                        <td>
                            <?php echo htmlspecialchars($claim['customer_name']); ?><br>
                            <small style="color: #6c757d;"><?php echo htmlspecialchars($claim['customer_email']); ?></small>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($claim['policy_name']); ?><br>
                            <small style="color: #6c757d;"><?php echo htmlspecialchars($claim['type_name']); ?></small>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($claim['incident_date'])); ?></td>
                        <td>KSH <?php echo number_format($claim['claim_amount'], 2); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $claim['status'])); ?>">
                                <?php echo $claim['status']; ?>
                            </span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($claim['created_at'])); ?></td>
                        <td>
                            <button onclick="viewClaim(<?php echo $claim['claim_id']; ?>)" class="btn btn-primary">View</button>
                            <?php if($claim['status'] == 'Pending' || $claim['status'] == 'Under Review'): ?>
                                <button onclick="processClaim(<?php echo $claim['claim_id']; ?>)" class="btn btn-warning">Process</button>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if(empty($claims)): ?>
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No claims found</h3>
                    <p>Claims will appear here when customers file them.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Process Claim Modal -->
    <div id="processClaimModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>Process Claim</h3>
            <form method="post" id="processClaimForm">
                <input type="hidden" name="claim_id" id="modalClaimId">
                
                <div style="margin-bottom: 15px;">
                    <label>Status:</label>
                    <select name="status" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="Under Review">Under Review</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                        <option value="Processed">Processed</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label>Processing Notes:</label>
                    <textarea name="notes" rows="4" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="Add notes about the processing decision..."></textarea>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" name="update_claim_status" class="btn btn-primary">Update Claim</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function searchClaims() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('claimsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length - 1; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                rows[i].style.display = found ? '' : 'none';
            }
        }

        function filterByStatus() {
            const select = document.getElementById('statusFilter');
            const filter = select.value;
            const table = document.getElementById('claimsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const statusCell = rows[i].getElementsByTagName('td')[5];
                if (filter === '' || statusCell.textContent.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }

        function viewClaim(claimId) {
            window.open('../view-claim.php?id=' + claimId, '_blank');
        }

        function processClaim(claimId) {
            document.getElementById('modalClaimId').value = claimId;
            document.getElementById('processClaimModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('processClaimModal').style.display = 'none';
        }

        window.onclick = function(event) {
            const modal = document.getElementById('processClaimModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
