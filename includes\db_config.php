<?php

$db_name = "zamara_db";
$db_user = "root";
$db_password = "";

$hosts_to_try = ["127.0.0.1", "localhost", "::1"];
$conn = null;
$last_error = "";

foreach ($hosts_to_try as $db_host) {
    try {
        $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        break;
    } catch(PDOException $e) {
        $last_error = $e->getMessage();
        continue;
    }
}

if ($conn === null) {
    die("ERROR: Could not connect to the database. Last error: " . $last_error . "<br><br>
    <strong>Troubleshooting steps:</strong><br>
    1. Make sure XAMPP MySQL service is running<br>
    2. Check if the database 'zamara_db' exists<br>
    3. Try restarting XAMPP MySQL service<br>
    4. Check XAMPP MySQL configuration");
}
?>