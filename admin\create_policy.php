<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$policy_name = $policy_type_id = $description = $duration = $premium_amount = $coverage_amount = $terms_conditions = "";
$policy_name_err = $policy_type_id_err = $premium_amount_err = $coverage_amount_err = "";
$success_message = $error_message = "";

// Get policy types for dropdown
$types_query = "SELECT policy_type_id, type_name FROM policy_types ORDER BY type_name";
$types_stmt = $conn->prepare($types_query);
$types_stmt->execute();
$policy_types = $types_stmt->fetchAll(PDO::FETCH_ASSOC);

if($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate policy name
    if(empty(trim($_POST["policy_name"]))) {
        $policy_name_err = "Please enter a policy name.";
    } else {
        $policy_name = trim($_POST["policy_name"]);
    }
    
    // Validate policy type
    if(empty(trim($_POST["policy_type_id"]))) {
        $policy_type_id_err = "Please select a policy type.";
    } else {
        $policy_type_id = trim($_POST["policy_type_id"]);
    }
    
    // Validate premium amount
    if(empty(trim($_POST["premium_amount"]))) {
        $premium_amount_err = "Please enter premium amount.";
    } elseif(!is_numeric(trim($_POST["premium_amount"])) || trim($_POST["premium_amount"]) <= 0) {
        $premium_amount_err = "Please enter a valid premium amount.";
    } else {
        $premium_amount = trim($_POST["premium_amount"]);
    }
    
    // Validate coverage amount
    if(empty(trim($_POST["coverage_amount"]))) {
        $coverage_amount_err = "Please enter coverage amount.";
    } elseif(!is_numeric(trim($_POST["coverage_amount"])) || trim($_POST["coverage_amount"]) <= 0) {
        $coverage_amount_err = "Please enter a valid coverage amount.";
    } else {
        $coverage_amount = trim($_POST["coverage_amount"]);
    }
    
    // Get optional fields
    $description = trim($_POST["description"]);
    $duration = trim($_POST["duration"]);
    $terms_conditions = trim($_POST["terms_conditions"]);
    
    // Check input errors before inserting in database
    if(empty($policy_name_err) && empty($policy_type_id_err) && empty($premium_amount_err) && empty($coverage_amount_err)) {
        try {
            $sql = "INSERT INTO policies (policy_name, policy_type_id, description, duration, premium_amount, coverage_amount, terms_conditions, created_by) 
                    VALUES (:policy_name, :policy_type_id, :description, :duration, :premium_amount, :coverage_amount, :terms_conditions, :created_by)";
            
            if($stmt = $conn->prepare($sql)) {
                $stmt->bindParam(":policy_name", $policy_name);
                $stmt->bindParam(":policy_type_id", $policy_type_id);
                $stmt->bindParam(":description", $description);
                $stmt->bindParam(":duration", $duration);
                $stmt->bindParam(":premium_amount", $premium_amount);
                $stmt->bindParam(":coverage_amount", $coverage_amount);
                $stmt->bindParam(":terms_conditions", $terms_conditions);
                $stmt->bindParam(":created_by", $_SESSION["user_id"]);
                
                if($stmt->execute()) {
                    $success_message = "Policy created successfully!";
                    
                    // Clear form
                    $policy_name = $policy_type_id = $description = $duration = $premium_amount = $coverage_amount = $terms_conditions = "";
                } else {
                    $error_message = "Something went wrong. Please try again later.";
                }
            }
        } catch(PDOException $e) {
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

$page_title = "Create New Policy";
$page_description = "Add new insurance policies to the system.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #218838; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .error { color: #dc3545; font-size: 14px; margin-top: 5px; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <div class="form-container">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label>Policy Name *</label>
                        <input type="text" name="policy_name" value="<?php echo $policy_name; ?>" required>
                        <span class="error"><?php echo $policy_name_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Policy Type *</label>
                        <select name="policy_type_id" required>
                            <option value="">Select Policy Type</option>
                            <?php foreach($policy_types as $type): ?>
                                <option value="<?php echo $type['policy_type_id']; ?>" <?php echo ($policy_type_id == $type['policy_type_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span class="error"><?php echo $policy_type_id_err; ?></span>
                    </div>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea name="description" rows="3"><?php echo $description; ?></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Duration</label>
                        <input type="text" name="duration" value="<?php echo $duration; ?>" placeholder="e.g., 12 months, 1 year">
                    </div>
                    <div class="form-group">
                        <!-- Empty for spacing -->
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Premium Amount (KSH) *</label>
                        <input type="number" name="premium_amount" value="<?php echo $premium_amount; ?>" step="0.01" min="0" required>
                        <span class="error"><?php echo $premium_amount_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Coverage Amount (KSH) *</label>
                        <input type="number" name="coverage_amount" value="<?php echo $coverage_amount; ?>" step="0.01" min="0" required>
                        <span class="error"><?php echo $coverage_amount_err; ?></span>
                    </div>
                </div>

                <div class="form-group">
                    <label>Terms & Conditions</label>
                    <textarea name="terms_conditions" rows="5"><?php echo $terms_conditions; ?></textarea>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button type="submit" class="btn">Create Policy</button>
                    <a href="dashboard.php" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
