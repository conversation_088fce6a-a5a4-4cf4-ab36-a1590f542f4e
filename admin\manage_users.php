<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_admin = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Administrator") {
        $is_admin = true;
        break;
    }
}

if(!$is_admin) {
    header("location: ../dashboard.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = "";
$error_message = "";

if($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['action'])) {
        switch($_POST['action']) {
            case 'create_user':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = password_hash(trim($_POST['password']), PASSWORD_DEFAULT);
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $phone = trim($_POST['phone']);
                $role_id = $_POST['role_id'];
                
                try {
                    $conn->beginTransaction();
                    
                    $query = "INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$username, $email, $password, $first_name, $last_name, $phone]);
                    $user_id = $conn->lastInsertId();
                    
                    $query = "INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$user_id, $role_id, $_SESSION['user_id']]);
                    
                    $conn->commit();
                    $success_message = "User created successfully!";
                } catch(Exception $e) {
                    $conn->rollBack();
                    $error_message = "Error creating user: " . $e->getMessage();
                }
                break;
                
            case 'update_status':
                $user_id = $_POST['user_id'];
                $status = $_POST['status'];
                
                $query = "UPDATE users SET status = ? WHERE user_id = ?";
                $stmt = $conn->prepare($query);
                if($stmt->execute([$status, $user_id])) {
                    $success_message = "User status updated successfully!";
                } else {
                    $error_message = "Error updating user status.";
                }
                break;
                
            case 'delete_user':
                $user_id = $_POST['user_id'];
                
                if($user_id == $_SESSION['user_id']) {
                    $error_message = "You cannot delete your own account.";
                } else {
                    $query = "DELETE FROM users WHERE user_id = ?";
                    $stmt = $conn->prepare($query);
                    if($stmt->execute([$user_id])) {
                        $success_message = "User deleted successfully!";
                    } else {
                        $error_message = "Error deleting user.";
                    }
                }
                break;
        }
    }
}

$query = "SELECT u.*, GROUP_CONCAT(r.role_name) as roles 
          FROM users u 
          LEFT JOIN user_roles ur ON u.user_id = ur.user_id 
          LEFT JOIN roles r ON ur.role_id = r.role_id 
          GROUP BY u.user_id 
          ORDER BY u.created_at DESC";
$users = $conn->query($query)->fetchAll(PDO::FETCH_ASSOC);

$roles_query = "SELECT * FROM roles ORDER BY role_name";
$roles = $conn->query($roles_query)->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Users";
$page_description = "Create, edit, and manage system users.";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .admin-header { background: #343a40; color: white; padding: 15px 0; margin-bottom: 30px; }
        .admin-nav { display: flex; justify-content: space-between; align-items: center; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .form-group { margin-bottom: 15px; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="admin-container">
            <div class="admin-nav">
                <h2>Zamara Admin - User Management</h2>
                <div>
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                    <a href="dashboard.php" class="btn btn-secondary" style="margin-left: 15px;">Dashboard</a>
                    <a href="../logout.php" class="btn btn-danger" style="margin-left: 10px;">Logout</a>
                </div>
            </div>
        </div>
    </header>

    <div class="admin-container">
        <?php if($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if($error_message): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1>User Management</h1>
            <button onclick="openModal('createUserModal')" class="btn btn-primary">Create New User</button>
        </div>

        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Roles</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($users as $user): ?>
                <tr>
                    <td><?php echo $user['user_id']; ?></td>
                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                    <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                    <td><?php echo htmlspecialchars($user['roles'] ?: 'No roles'); ?></td>
                    <td>
                        <span class="badge <?php echo $user['status'] == 'Active' ? 'btn-success' : 'btn-warning'; ?>">
                            <?php echo $user['status']; ?>
                        </span>
                    </td>
                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                    <td>
                        <?php if($user['user_id'] != $_SESSION['user_id']): ?>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                <select name="status" onchange="this.form.submit()" class="btn btn-sm">
                                    <option value="Active" <?php echo $user['status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="Inactive" <?php echo $user['status'] == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="Suspended" <?php echo $user['status'] == 'Suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </form>
                            
                            <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                <input type="hidden" name="action" value="delete_user">
                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        <?php else: ?>
                            <span class="text-muted">Current User</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Create User Modal -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createUserModal')">&times;</span>
            <h3>Create New User</h3>
            <form method="post">
                <input type="hidden" name="action" value="create_user">
                
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" name="username" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="email" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="password" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" name="first_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>Last Name:</label>
                    <input type="text" name="last_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>Phone:</label>
                    <input type="text" name="phone" class="form-control">
                </div>
                
                <div class="form-group">
                    <label>Role:</label>
                    <select name="role_id" class="form-control" required>
                        <option value="">Select Role</option>
                        <?php foreach($roles as $role): ?>
                            <option value="<?php echo $role['role_id']; ?>"><?php echo htmlspecialchars($role['role_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" onclick="closeModal('createUserModal')" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
