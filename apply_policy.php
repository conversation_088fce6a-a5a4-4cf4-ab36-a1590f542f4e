<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

require_once "includes/db_config.php";

$policy_id = $start_date = $payment_frequency = "";
$policy_id_err = $start_date_err = $payment_frequency_err = "";
$success_message = $error_message = "";


if(isset($_GET["id"]) && !empty($_GET["id"])) {
    $policy_id = $_GET["id"];
}


$policies = [
    [
        'policy_id' => 1,
        'policy_name' => 'Comprehensive Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Provides coverage for damage to your vehicle as well as third-party liability.',
        'duration' => '12 months',
        'premium_amount' => 15000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers damage to your vehicle due to accidents, theft, fire, and natural disasters. It also covers third-party liability for bodily injury and property damage.'
    ],
    [
        'policy_id' => 2,
        'policy_name' => 'Basic Health Insurance',
        'type_name' => 'Health',
        'description' => 'Covers basic medical expenses including hospitalization, surgery, and prescription drugs.',
        'duration' => '12 months',
        'premium_amount' => 12000,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers hospitalization, surgery, and prescription drugs. Pre-existing conditions may have a waiting period of 6 months. Dental and vision care are not covered.'
    ],
    [
        'policy_id' => 3,
        'policy_name' => 'Term Life Insurance',
        'type_name' => 'Life',
        'description' => 'Provides financial protection to your beneficiaries in case of your death during the policy term.',
        'duration' => '10 years',
        'premium_amount' => 8000,
        'coverage_amount' => 2000000,
        'terms_conditions' => 'This policy provides a death benefit to your beneficiaries if you die during the policy term. Suicide is not covered in the first two years of the policy.'
    ],
    [
        'policy_id' => 4,
        'policy_name' => 'Business Property Insurance',
        'type_name' => 'Business',
        'description' => 'Protects your business property against damage or loss due to fire, theft, and natural disasters.',
        'duration' => '12 months',
        'premium_amount' => 25000,
        'coverage_amount' => 5000000,
        'terms_conditions' => 'This policy covers damage to your business property due to fire, theft, and natural disasters. Business interruption coverage is included for up to 3 months.'
    ],
    [
        'policy_id' => 5,
        'policy_name' => 'Third-Party Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Basic coverage for third-party liability only, as required by law.',
        'duration' => '12 months',
        'premium_amount' => 7500,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers third-party liability for bodily injury and property damage. It does not cover damage to your own vehicle.'
    ],
    [
        'policy_id' => 6,
        'policy_name' => 'Commercial Vehicle Insurance',
        'type_name' => 'Motor',
        'description' => 'Specialized coverage for commercial vehicles including trucks, vans, and taxis.',
        'duration' => '12 months',
        'premium_amount' => 20000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers commercial vehicles for damage, theft, and third-party liability. Additional coverage for goods in transit is available.'
    ],
    [
        'policy_id' => 7,
        'policy_name' => 'Premium Health Insurance',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage including dental, vision, and mental health services.',
        'duration' => '12 months',
        'premium_amount' => 24000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers all medical expenses including hospitalization, surgery, prescription drugs, dental, vision, and mental health services. Pre-existing conditions have a waiting period of 3 months.'
    ],
    [
        'policy_id' => 8,
        'policy_name' => 'Family Health Plan',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage for the entire family with special benefits for children.',
        'duration' => '12 months',
        'premium_amount' => 36000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers all medical expenses for up to 4 family members. Special benefits include pediatric care, maternity coverage, and preventive health checkups.'
    ],
    [
        'policy_id' => 9,
        'policy_name' => 'Whole Life Insurance',
        'type_name' => 'Life',
        'description' => 'Lifetime coverage with a cash value component that grows over time.',
        'duration' => 'Lifetime',
        'premium_amount' => 15000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy provides lifetime coverage with a cash value component that grows over time. You can borrow against the cash value or surrender the policy for its cash value.'
    ],
    [
        'policy_id' => 10,
        'policy_name' => 'Endowment Policy',
        'type_name' => 'Life',
        'description' => 'Combines life insurance with savings, providing a lump sum payment at the end of the term.',
        'duration' => '15 years',
        'premium_amount' => 12000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy provides life insurance coverage and a lump sum payment at the end of the term. Premiums are higher than term life insurance but provide a guaranteed return.'
    ],
    [
        'policy_id' => 11,
        'policy_name' => 'Professional Liability Insurance',
        'type_name' => 'Business',
        'description' => 'Protects professionals against claims of negligence or failure to perform their professional duties.',
        'duration' => '12 months',
        'premium_amount' => 18000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy covers legal costs and damages awarded in claims of professional negligence. It does not cover criminal prosecution or certain liabilities that may be specified in the policy.'
    ],
    [
        'policy_id' => 12,
        'policy_name' => 'Workers\' Compensation Insurance',
        'type_name' => 'Business',
        'description' => 'Provides coverage for employee injuries or illnesses that occur as a result of their job.',
        'duration' => '12 months',
        'premium_amount' => 30000,
        'coverage_amount' => 4000000,
        'terms_conditions' => 'This policy covers medical expenses, rehabilitation costs, and lost wages for employees who are injured or become ill as a result of their job. It also provides death benefits to dependents of workers who are killed on the job.'
    ]
];


$policy = null;
foreach ($policies as $p) {
    if ($p['policy_id'] == $policy_id) {
        $policy = $p;
        break;
    }
}


if (!$policy) {
    header("location: browse_policies.php");
    exit;
}


if($_SERVER["REQUEST_METHOD"] == "POST") {


    if(empty(trim($_POST["start_date"]))) {
        $start_date_err = "Please select a start date.";
    } else {
        $start_date = trim($_POST["start_date"]);


        if(strtotime($start_date) < strtotime(date('Y-m-d'))) {
            $start_date_err = "Start date must be today or in the future.";
        }
    }


    if(empty(trim($_POST["payment_frequency"]))) {
        $payment_frequency_err = "Please select a payment frequency.";
    } else {
        $payment_frequency = trim($_POST["payment_frequency"]);
    }


    if(empty($start_date_err)) {
        $duration_parts = explode(' ', $policy['duration']);
        $duration_value = intval($duration_parts[0]);
        $duration_unit = strtolower($duration_parts[1]);

        if($duration_unit == 'months' || $duration_unit == 'month') {
            $end_date = date('Y-m-d', strtotime($start_date . ' + ' . $duration_value . ' months'));
        } elseif($duration_unit == 'years' || $duration_unit == 'year') {
            $end_date = date('Y-m-d', strtotime($start_date . ' + ' . $duration_value . ' years'));
        } else {
            $end_date = date('Y-m-d', strtotime($start_date . ' + 1 year')); // Default to 1 year
        }


        switch($payment_frequency) {
            case 'Monthly':
                $next_payment_date = date('Y-m-d', strtotime($start_date . ' + 1 month'));
                break;
            case 'Quarterly':
                $next_payment_date = date('Y-m-d', strtotime($start_date . ' + 3 months'));
                break;
            case 'Semi-Annually':
                $next_payment_date = date('Y-m-d', strtotime($start_date . ' + 6 months'));
                break;
            case 'Annually':
                $next_payment_date = date('Y-m-d', strtotime($start_date . ' + 1 year'));
                break;
            default:
                $next_payment_date = date('Y-m-d', strtotime($start_date . ' + 1 month')); // Default to monthly
        }
    }


    if(empty($start_date_err) && empty($payment_frequency_err)) {
        try {

            $conn->beginTransaction();


            $check_query = "SELECT policy_id FROM policies WHERE policy_id = :policy_id";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bindParam(":policy_id", $policy_id);
            $check_stmt->execute();


            if($check_stmt->rowCount() == 0) {

                $type_query = "SELECT policy_type_id FROM policy_types WHERE type_name = :type_name";
                $type_stmt = $conn->prepare($type_query);
                $type_stmt->bindParam(":type_name", $policy['type_name']);
                $type_stmt->execute();

                if($type_stmt->rowCount() > 0) {
                    $policy_type_id = $type_stmt->fetch(PDO::FETCH_ASSOC)['policy_type_id'];
                } else {

                    $insert_type_query = "INSERT INTO policy_types (type_name, description) VALUES (:type_name, :description)";
                    $insert_type_stmt = $conn->prepare($insert_type_query);
                    $insert_type_stmt->bindParam(":type_name", $policy['type_name']);
                    $insert_type_stmt->bindValue(":description", "Insurance for " . $policy['type_name']);
                    $insert_type_stmt->execute();
                    $policy_type_id = $conn->lastInsertId();
                }


                $admin_check_query = "SELECT user_id FROM users WHERE username = 'admin' LIMIT 1";
                $admin_check_stmt = $conn->prepare($admin_check_query);
                $admin_check_stmt->execute();

                if($admin_check_stmt->rowCount() == 0) {

                    $admin_insert_query = "INSERT INTO users (username, password, email, first_name, last_name)
                                          VALUES ('admin', '$2y$10$8KzO1f8Yd7aX9HQ6Yg9Zp.xJnFmBTPm/CnGlHMfVfOEYcH2sjnVOO', '<EMAIL>', 'Admin', 'User')";
                    $conn->exec($admin_insert_query);
                    $admin_id = $conn->lastInsertId();


                    $role_query = "SELECT role_id FROM roles WHERE role_name = 'Administrator' LIMIT 1";
                    $role_stmt = $conn->prepare($role_query);
                    $role_stmt->execute();

                    if($role_stmt->rowCount() > 0) {
                        $role_id = $role_stmt->fetch(PDO::FETCH_ASSOC)['role_id'];


                        $role_assign_query = "INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)";
                        $role_assign_stmt = $conn->prepare($role_assign_query);
                        $role_assign_stmt->bindParam(":user_id", $admin_id);
                        $role_assign_stmt->bindParam(":role_id", $role_id);
                        $role_assign_stmt->execute();
                    }
                } else {
                    $admin_id = $admin_check_stmt->fetch(PDO::FETCH_ASSOC)['user_id'];
                }


                $insert_policy_query = "INSERT INTO policies (policy_id, policy_name, policy_type_id, description, duration, premium_amount, coverage_amount, terms_conditions, created_by)
                                       VALUES (:policy_id, :policy_name, :policy_type_id, :description, :duration, :premium_amount, :coverage_amount, :terms_conditions, :created_by)";

                $insert_policy_stmt = $conn->prepare($insert_policy_query);
                $insert_policy_stmt->bindParam(":policy_id", $policy_id);
                $insert_policy_stmt->bindParam(":policy_name", $policy['policy_name']);
                $insert_policy_stmt->bindParam(":policy_type_id", $policy_type_id);
                $insert_policy_stmt->bindParam(":description", $policy['description']);
                $insert_policy_stmt->bindParam(":duration", $policy['duration']);
                $insert_policy_stmt->bindParam(":premium_amount", $policy['premium_amount']);
                $insert_policy_stmt->bindParam(":coverage_amount", $policy['coverage_amount']);
                $insert_policy_stmt->bindParam(":terms_conditions", $policy['terms_conditions']);
                $insert_policy_stmt->bindParam(":created_by", $admin_id);
                $insert_policy_stmt->execute();
            }


            $policy_number = "POL-" . date("Ymd") . "-" . rand(1000, 9999);


            $query = "INSERT INTO customer_policies (customer_id, policy_id, policy_number, start_date, end_date, status, total_premium, payment_frequency, next_payment_date)
                      VALUES (:customer_id, :policy_id, :policy_number, :start_date, :end_date, 'Active', :total_premium, :payment_frequency, :next_payment_date)";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(":customer_id", $_SESSION["user_id"]);
            $stmt->bindParam(":policy_id", $policy_id);
            $stmt->bindParam(":policy_number", $policy_number);
            $stmt->bindParam(":start_date", $start_date);
            $stmt->bindParam(":end_date", $end_date);
            $stmt->bindParam(":total_premium", $policy['premium_amount']);
            $stmt->bindParam(":payment_frequency", $payment_frequency);
            $stmt->bindParam(":next_payment_date", $next_payment_date);

            $stmt->execute();
            $customer_policy_id = $conn->lastInsertId();


            $conn->commit();

            $success_message = "Your policy application has been submitted successfully. Policy Number: " . $policy_number;
        } catch(PDOException $e) {

            $conn->rollBack();
            $error_message = "Something went wrong: " . $e->getMessage();
        }
    }
}

$page_title = "Apply for " . ($policy ? $policy['policy_name'] : "Policy");
$page_description = "Apply for " . ($policy ? $policy['policy_name'] : "insurance policy") . " with Zamara Insurance.";

include_once 'includes/header.php';
?>

<section style="padding: 60px 0;">
    <div class="container">
        <h1 style="margin-bottom: 30px; color: #0056b3;">Apply for <?php echo htmlspecialchars($policy['policy_name']); ?></h1>

        <div style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
            <div style="flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0 15px;">
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                    <?php
                    if(!empty($success_message)) {
                        echo '<div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724;">' . $success_message . '</div>';
                    }

                    if(!empty($error_message)) {
                        echo '<div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;">' . $error_message . '</div>';
                    }
                    ?>

                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $policy_id); ?>" method="post">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Policy Name</label>
                            <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; background-color: #e9ecef;" value="<?php echo htmlspecialchars($policy['policy_name']); ?>" readonly>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Policy Type</label>
                            <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; background-color: #e9ecef;" value="<?php echo htmlspecialchars($policy['type_name']); ?>" readonly>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Premium Amount</label>
                            <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; background-color: #e9ecef;" value="KSH <?php echo number_format($policy['premium_amount'], 2); ?>" readonly>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Coverage Amount</label>
                            <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; background-color: #e9ecef;" value="KSH <?php echo number_format($policy['coverage_amount'], 2); ?>" readonly>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Duration</label>
                            <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; background-color: #e9ecef;" value="<?php echo htmlspecialchars($policy['duration']); ?>" readonly>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label for="start_date" style="display: block; margin-bottom: 5px; font-weight: 500;">Start Date *</label>
                            <input type="date" name="start_date" id="start_date" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $start_date; ?>" min="<?php echo date('Y-m-d'); ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $start_date_err; ?></span>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label for="payment_frequency" style="display: block; margin-bottom: 5px; font-weight: 500;">Payment Frequency *</label>
                            <select name="payment_frequency" id="payment_frequency" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                                <option value="">-- Select Payment Frequency --</option>
                                <option value="Monthly" <?php echo ($payment_frequency == "Monthly") ? 'selected' : ''; ?>>Monthly</option>
                                <option value="Quarterly" <?php echo ($payment_frequency == "Quarterly") ? 'selected' : ''; ?>>Quarterly</option>
                                <option value="Semi-Annually" <?php echo ($payment_frequency == "Semi-Annually") ? 'selected' : ''; ?>>Semi-Annually</option>
                                <option value="Annually" <?php echo ($payment_frequency == "Annually") ? 'selected' : ''; ?>>Annually</option>
                            </select>
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $payment_frequency_err; ?></span>
                        </div>

                        <div style="margin-top: 30px;">
                            <button type="submit" style="padding: 12px 25px; background-color: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Apply Now</button>
                            <a href="policy_details.php?id=<?php echo $policy_id; ?>" style="display: inline-block; margin-left: 10px; padding: 12px 25px; background-color: #6c757d; color: white; border: none; border-radius: 4px; text-decoration: none; font-size: 16px; font-weight: 500;">Back to Policy Details</a>
                        </div>
                    </form>
                </div>
            </div>

            <div style="flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0 15px;">
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Policy Summary</h3>
                    <p style="margin-bottom: 15px;"><strong>Policy Name:</strong> <?php echo htmlspecialchars($policy['policy_name']); ?></p>
                    <p style="margin-bottom: 15px;"><strong>Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                    <p style="margin-bottom: 15px;"><strong>Premium:</strong> KSH <?php echo number_format($policy['premium_amount'], 2); ?></p>
                    <p style="margin-bottom: 15px;"><strong>Coverage:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>
                    <p style="margin-bottom: 15px;"><strong>Duration:</strong> <?php echo htmlspecialchars($policy['duration']); ?></p>
                    <p style="margin-bottom: 15px;"><strong>Description:</strong> <?php echo htmlspecialchars($policy['description']); ?></p>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Terms & Conditions</h3>
                    <p style="margin-bottom: 15px;"><?php echo nl2br(htmlspecialchars($policy['terms_conditions'])); ?></p>
                    <div style="margin-top: 20px;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" style="margin-right: 10px;" required>
                            <span>I agree to the terms and conditions</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>