<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$username = $email = $password = $first_name = $last_name = $phone = $address = $date_of_birth = $gender = "";
$username_err = $email_err = $password_err = $first_name_err = $last_name_err = "";
$success_message = $error_message = "";

if($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate username
    if(empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        $sql = "SELECT user_id FROM users WHERE username = :username";
        if($stmt = $conn->prepare($sql)) {
            $stmt->bindParam(":username", $param_username, PDO::PARAM_STR);
            $param_username = trim($_POST["username"]);
            if($stmt->execute()) {
                if($stmt->rowCount() == 1) {
                    $username_err = "This username is already taken.";
                } else {
                    $username = trim($_POST["username"]);
                }
            }
        }
    }
    
    // Validate email
    if(empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } elseif(!filter_var(trim($_POST["email"]), FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email.";
    } else {
        $sql = "SELECT user_id FROM users WHERE email = :email";
        if($stmt = $conn->prepare($sql)) {
            $stmt->bindParam(":email", $param_email, PDO::PARAM_STR);
            $param_email = trim($_POST["email"]);
            if($stmt->execute()) {
                if($stmt->rowCount() == 1) {
                    $email_err = "This email is already registered.";
                } else {
                    $email = trim($_POST["email"]);
                }
            }
        }
    }
    
    // Validate password
    if(empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";
    } elseif(strlen(trim($_POST["password"])) < 6) {
        $password_err = "Password must have at least 6 characters.";
    } else {
        $password = trim($_POST["password"]);
    }
    
    // Validate other fields
    if(empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter first name.";
    } else {
        $first_name = trim($_POST["first_name"]);
    }
    
    if(empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter last name.";
    } else {
        $last_name = trim($_POST["last_name"]);
    }
    
    // Get optional fields
    $phone = trim($_POST["phone"]);
    $address = trim($_POST["address"]);
    $date_of_birth = !empty($_POST["date_of_birth"]) ? $_POST["date_of_birth"] : null;
    $gender = !empty($_POST["gender"]) ? $_POST["gender"] : null;
    
    // Check input errors before inserting in database
    if(empty($username_err) && empty($email_err) && empty($password_err) && empty($first_name_err) && empty($last_name_err)) {
        try {
            $conn->beginTransaction();
            
            // Insert user
            $sql = "INSERT INTO users (username, email, password, first_name, last_name, phone, address, date_of_birth, gender) 
                    VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address, :date_of_birth, :gender)";
            
            if($stmt = $conn->prepare($sql)) {
                $stmt->bindParam(":username", $username);
                $stmt->bindParam(":email", $email);
                $stmt->bindParam(":password", $param_password);
                $stmt->bindParam(":first_name", $first_name);
                $stmt->bindParam(":last_name", $last_name);
                $stmt->bindParam(":phone", $phone);
                $stmt->bindParam(":address", $address);
                $stmt->bindParam(":date_of_birth", $date_of_birth);
                $stmt->bindParam(":gender", $gender);
                
                $param_password = password_hash($password, PASSWORD_DEFAULT);
                
                if($stmt->execute()) {
                    $user_id = $conn->lastInsertId();
                    
                    // Get Customer role ID
                    $role_query = "SELECT role_id FROM roles WHERE role_name = 'Customer'";
                    $role_stmt = $conn->prepare($role_query);
                    $role_stmt->execute();
                    $customer_role = $role_stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if($customer_role) {
                        // Assign Customer role
                        $role_sql = "INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (:user_id, :role_id, :assigned_by)";
                        $role_stmt = $conn->prepare($role_sql);
                        $role_stmt->bindParam(":user_id", $user_id);
                        $role_stmt->bindParam(":role_id", $customer_role['role_id']);
                        $role_stmt->bindParam(":assigned_by", $_SESSION["user_id"]);
                        
                        if($role_stmt->execute()) {
                            $conn->commit();
                            $success_message = "Customer created successfully! Username: $username";
                            
                            // Clear form
                            $username = $email = $password = $first_name = $last_name = $phone = $address = $date_of_birth = $gender = "";
                        } else {
                            $conn->rollback();
                            $error_message = "Error assigning customer role.";
                        }
                    } else {
                        $conn->rollback();
                        $error_message = "Customer role not found.";
                    }
                } else {
                    $conn->rollback();
                    $error_message = "Something went wrong. Please try again later.";
                }
            }
        } catch(PDOException $e) {
            $conn->rollback();
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

$page_title = "Add New Customer";
$page_description = "Create a new customer account.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #218838; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .error { color: #dc3545; font-size: 14px; margin-top: 5px; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="customers.php" class="back-link">← Back to Customers</a>
        
        <div class="form-container">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label>Username *</label>
                        <input type="text" name="username" value="<?php echo $username; ?>" required>
                        <span class="error"><?php echo $username_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Email *</label>
                        <input type="email" name="email" value="<?php echo $email; ?>" required>
                        <span class="error"><?php echo $email_err; ?></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>First Name *</label>
                        <input type="text" name="first_name" value="<?php echo $first_name; ?>" required>
                        <span class="error"><?php echo $first_name_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Last Name *</label>
                        <input type="text" name="last_name" value="<?php echo $last_name; ?>" required>
                        <span class="error"><?php echo $last_name_err; ?></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Password *</label>
                        <input type="password" name="password" required>
                        <span class="error"><?php echo $password_err; ?></span>
                    </div>
                    <div class="form-group">
                        <label>Phone</label>
                        <input type="tel" name="phone" value="<?php echo $phone; ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Date of Birth</label>
                        <input type="date" name="date_of_birth" value="<?php echo $date_of_birth; ?>">
                    </div>
                    <div class="form-group">
                        <label>Gender</label>
                        <select name="gender">
                            <option value="">Select Gender</option>
                            <option value="Male" <?php echo ($gender == 'Male') ? 'selected' : ''; ?>>Male</option>
                            <option value="Female" <?php echo ($gender == 'Female') ? 'selected' : ''; ?>>Female</option>
                            <option value="Other" <?php echo ($gender == 'Other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>Address</label>
                    <textarea name="address" rows="3"><?php echo $address; ?></textarea>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button type="submit" class="btn">Create Customer</button>
                    <a href="customers.php" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
