#!/bin/bash

# Find all PHP files
find . -name "*.php" -type f | while read file; do
  # Remove PHP single-line comments (// ...)
  sed -i 's/\/\/.*$//' "$file"
  
  # Remove HTML comments (<!-- ... -->)
  sed -i 's/<!--.*-->//' "$file"
done

# Find all JavaScript files
find . -name "*.js" -type f | while read file; do
  # Remove JavaScript single-line comments (// ...)
  sed -i 's/\/\/.*$//' "$file"
done

# Find all CSS files
find . -name "*.css" -type f | while read file; do
  # Remove CSS comments (/* ... */)
  sed -i 's/\/\*.*\*\///' "$file"
done

echo "All comments have been removed from PHP, JS, and CSS files."
