<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$payment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($payment_id <= 0) {
    header("location: payments.php");
    exit;
}

// Get payment details
$query = "SELECT p.*, cp.policy_number, pol.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email, customer.phone as customer_phone,
                 CONCAT(recorder.first_name, ' ', recorder.last_name) as recorded_by_name,
                 CONCAT(verifier.first_name, ' ', verifier.last_name) as verified_by_name
          FROM payments p
          JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
          JOIN policies pol ON cp.policy_id = pol.policy_id
          JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          LEFT JOIN users recorder ON p.recorded_by = recorder.user_id
          LEFT JOIN users verifier ON p.verified_by = verifier.user_id
          WHERE p.payment_id = :payment_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':payment_id', $payment_id);
$stmt->execute();
$payment = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$payment) {
    header("location: payments.php");
    exit;
}

$page_title = "Payment Details - " . $payment['payment_number'];
$page_description = "View detailed payment information.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #343a40; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .payment-header { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: start; margin-bottom: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .status-badge { padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .status-refunded { background-color: #d1ecf1; color: #0c5460; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #343a40; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; }
        .info-section h4 { color: #343a40; margin-bottom: 15px; border-bottom: 2px solid #343a40; padding-bottom: 5px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: 600; color: #6c757d; font-size: 12px; margin-bottom: 3px; }
        .info-value { font-size: 14px; }
        .amount-display { font-size: 28px; font-weight: bold; color: #28a745; text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 8px; margin: 20px 0; }
        .timeline { border-left: 3px solid #343a40; padding-left: 20px; margin-left: 10px; }
        .timeline-item { margin-bottom: 20px; position: relative; }
        .timeline-item::before { content: ''; position: absolute; left: -26px; top: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: #343a40; }
        .timeline-date { font-size: 12px; color: #6c757d; }
        .timeline-content { margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="payments.php" class="back-link">← Back to Payments</a>
        
        <!-- Payment Header -->
        <div class="content">
            <div class="payment-header">
                <div>
                    <h2>Payment #<?php echo htmlspecialchars($payment['payment_number']); ?></h2>
                    <p style="color: #6c757d; margin-bottom: 10px;">
                        Payment ID: <?php echo $payment['payment_id']; ?> | 
                        Date: <?php echo date('M d, Y H:i', strtotime($payment['payment_date'])); ?>
                    </p>
                    <span class="status-badge status-<?php echo strtolower($payment['status']); ?>">
                        <?php echo $payment['status']; ?>
                    </span>
                </div>
                <div style="display: flex; gap: 10px; flex-direction: column;">
                    <button onclick="printPayment()" class="btn btn-primary">Print Receipt</button>
                    <a href="receipt.php?payment_id=<?php echo $payment['payment_id']; ?>" target="_blank" class="btn btn-info">Generate Receipt</a>
                    <?php if($payment['status'] == 'Pending'): ?>
                        <button onclick="verifyPayment()" class="btn btn-warning">Verify Payment</button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Payment Amount -->
            <div class="amount-display">
                KSH <?php echo number_format($payment['amount'], 2); ?>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="info-grid">
            <!-- Customer Information -->
            <div class="info-section">
                <h4>Customer Information</h4>
                <div class="info-item">
                    <div class="info-label">CUSTOMER NAME</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['customer_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">EMAIL</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['customer_email']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">PHONE</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['customer_phone'] ?: 'Not provided'); ?></div>
                </div>
            </div>

            <!-- Policy Information -->
            <div class="info-section">
                <h4>Policy Information</h4>
                <div class="info-item">
                    <div class="info-label">POLICY NUMBER</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['policy_number']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">POLICY NAME</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['policy_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">POLICY TYPE</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['type_name']); ?></div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="info-section">
                <h4>Payment Information</h4>
                <div class="info-item">
                    <div class="info-label">PAYMENT METHOD</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['payment_method']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">TRANSACTION REFERENCE</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['transaction_reference'] ?: 'N/A'); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">PAYMENT DATE</div>
                    <div class="info-value"><?php echo date('M d, Y H:i:s', strtotime($payment['payment_date'])); ?></div>
                </div>
            </div>

            <!-- System Information -->
            <div class="info-section">
                <h4>System Information</h4>
                <div class="info-item">
                    <div class="info-label">RECORDED BY</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['recorded_by_name'] ?: 'System'); ?></div>
                </div>
                <?php if($payment['verified_by_name']): ?>
                <div class="info-item">
                    <div class="info-label">VERIFIED BY</div>
                    <div class="info-value"><?php echo htmlspecialchars($payment['verified_by_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">VERIFIED AT</div>
                    <div class="info-value"><?php echo date('M d, Y H:i:s', strtotime($payment['verified_at'])); ?></div>
                </div>
                <?php endif; ?>
                <div class="info-item">
                    <div class="info-label">CREATED AT</div>
                    <div class="info-value"><?php echo date('M d, Y H:i:s', strtotime($payment['created_at'])); ?></div>
                </div>
            </div>
        </div>

        <!-- Payment Notes -->
        <?php if($payment['notes']): ?>
        <div class="content">
            <h4>Payment Notes</h4>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
                <?php echo nl2br(htmlspecialchars($payment['notes'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Payment Timeline -->
        <div class="content">
            <h4>Payment Timeline</h4>
            <div class="timeline" style="margin-top: 20px;">
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y H:i:s', strtotime($payment['created_at'])); ?></div>
                    <div class="timeline-content">
                        <strong>Payment Recorded</strong><br>
                        Payment recorded by <?php echo htmlspecialchars($payment['recorded_by_name'] ?: 'System'); ?>
                    </div>
                </div>
                
                <?php if($payment['verified_at']): ?>
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y H:i:s', strtotime($payment['verified_at'])); ?></div>
                    <div class="timeline-content">
                        <strong>Payment Verified</strong><br>
                        Payment verified by <?php echo htmlspecialchars($payment['verified_by_name']); ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y H:i:s', strtotime($payment['payment_date'])); ?></div>
                    <div class="timeline-content">
                        <strong>Payment Date</strong><br>
                        Actual payment transaction date
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="content">
            <div style="display: flex; gap: 10px; justify-content: center;">
                <a href="payments.php" class="btn btn-secondary">Back to Payments</a>
                <button onclick="printPayment()" class="btn btn-primary">Print Details</button>
                <a href="receipt.php?payment_id=<?php echo $payment['payment_id']; ?>" target="_blank" class="btn btn-info">Download Receipt</a>
                <?php if($payment['status'] == 'Completed'): ?>
                    <button onclick="refundPayment()" class="btn btn-warning">Process Refund</button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function printPayment() {
            window.print();
        }

        function verifyPayment() {
            if(confirm('Mark this payment as verified?')) {
                window.location.href = 'verify_payment.php?payment_id=<?php echo $payment['payment_id']; ?>';
            }
        }

        function refundPayment() {
            const reason = prompt('Enter refund reason:');
            if(reason) {
                if(confirm('Process refund for this payment?')) {
                    window.location.href = 'refund_payment.php?payment_id=<?php echo $payment['payment_id']; ?>&reason=' + encodeURIComponent(reason);
                }
            }
        }

        // Print styles
        window.addEventListener('beforeprint', function() {
            document.querySelector('.header').style.display = 'none';
            document.querySelector('.back-link').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.style.display = 'none');
        });

        window.addEventListener('afterprint', function() {
            document.querySelector('.header').style.display = 'block';
            document.querySelector('.back-link').style.display = 'inline-block';
            document.querySelectorAll('.btn').forEach(btn => btn.style.display = 'inline-block');
        });
    </script>
</body>
</html>
