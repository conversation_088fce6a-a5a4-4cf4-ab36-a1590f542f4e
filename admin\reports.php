<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$page_title = "Generate Reports";
$page_description = "View system analytics and generate reports.";

// Get system statistics
try {
    // Total users by role
    $users_query = "SELECT r.role_name, COUNT(ur.user_id) as count 
                    FROM roles r 
                    LEFT JOIN user_roles ur ON r.role_id = ur.role_id 
                    GROUP BY r.role_id, r.role_name 
                    ORDER BY r.role_name";
    $users_stmt = $conn->prepare($users_query);
    $users_stmt->execute();
    $user_stats = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Policy statistics
    $policy_stats_query = "SELECT 
                            pt.type_name,
                            COUNT(p.policy_id) as total_policies,
                            SUM(p.premium_amount) as total_premiums,
                            AVG(p.premium_amount) as avg_premium,
                            SUM(p.coverage_amount) as total_coverage
                          FROM policy_types pt 
                          LEFT JOIN policies p ON pt.policy_type_id = p.policy_type_id 
                          GROUP BY pt.policy_type_id, pt.type_name 
                          ORDER BY pt.type_name";
    $policy_stmt = $conn->prepare($policy_stats_query);
    $policy_stmt->execute();
    $policy_stats = $policy_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Claims statistics
    $claims_query = "SELECT 
                      status,
                      COUNT(*) as count,
                      SUM(claim_amount) as total_amount,
                      AVG(claim_amount) as avg_amount
                     FROM claims 
                     GROUP BY status 
                     ORDER BY status";
    $claims_stmt = $conn->prepare($claims_query);
    $claims_stmt->execute();
    $claims_stats = $claims_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Payment statistics
    $payments_query = "SELECT 
                        status,
                        COUNT(*) as count,
                        SUM(amount) as total_amount
                       FROM payments 
                       GROUP BY status 
                       ORDER BY status";
    $payments_stmt = $conn->prepare($payments_query);
    $payments_stmt->execute();
    $payments_stats = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Recent activity
    $recent_users_query = "SELECT username, first_name, last_name, created_at 
                           FROM users 
                           ORDER BY created_at DESC 
                           LIMIT 10";
    $recent_users_stmt = $conn->prepare($recent_users_query);
    $recent_users_stmt->execute();
    $recent_users = $recent_users_stmt->fetchAll(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    $error_message = "Error fetching reports: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #ffc107; color: #212529; padding: 20px 0; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card h3 { color: #0056b3; margin-bottom: 15px; border-bottom: 2px solid #0056b3; padding-bottom: 10px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table th, .table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #ffc107; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .export-btn { background-color: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .export-btn:hover { background-color: #138496; }
        .summary-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .summary-item { background-color: #e9ecef; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-number { font-size: 24px; font-weight: bold; color: #0056b3; }
        .summary-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <!-- Summary Statistics -->
        <div class="summary-stats">
            <?php
            $total_users = array_sum(array_column($user_stats, 'count'));
            $total_policies = array_sum(array_column($policy_stats, 'total_policies'));
            $total_claims = array_sum(array_column($claims_stats, 'count'));
            $total_payments = array_sum(array_column($payments_stats, 'count'));
            ?>
            <div class="summary-item">
                <div class="summary-number"><?php echo $total_users; ?></div>
                <div class="summary-label">Total Users</div>
            </div>
            <div class="summary-item">
                <div class="summary-number"><?php echo $total_policies; ?></div>
                <div class="summary-label">Total Policies</div>
            </div>
            <div class="summary-item">
                <div class="summary-number"><?php echo $total_claims; ?></div>
                <div class="summary-label">Total Claims</div>
            </div>
            <div class="summary-item">
                <div class="summary-number"><?php echo $total_payments; ?></div>
                <div class="summary-label">Total Payments</div>
            </div>
        </div>

        <div class="stats-grid">
            <!-- Users by Role -->
            <div class="stat-card">
                <h3>Users by Role</h3>
                <button class="export-btn" onclick="exportTable('users-table', 'users_by_role.csv')">Export CSV</button>
                <table class="table" id="users-table">
                    <thead>
                        <tr>
                            <th>Role</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($user_stats as $stat): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($stat['role_name']); ?></td>
                            <td><?php echo $stat['count']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Policy Statistics -->
            <div class="stat-card">
                <h3>Policy Statistics</h3>
                <button class="export-btn" onclick="exportTable('policies-table', 'policy_statistics.csv')">Export CSV</button>
                <table class="table" id="policies-table">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Count</th>
                            <th>Total Premiums</th>
                            <th>Avg Premium</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($policy_stats as $stat): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($stat['type_name']); ?></td>
                            <td><?php echo $stat['total_policies']; ?></td>
                            <td>KSH <?php echo number_format($stat['total_premiums'], 2); ?></td>
                            <td>KSH <?php echo number_format($stat['avg_premium'], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Claims Statistics -->
            <div class="stat-card">
                <h3>Claims by Status</h3>
                <button class="export-btn" onclick="exportTable('claims-table', 'claims_statistics.csv')">Export CSV</button>
                <table class="table" id="claims-table">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Count</th>
                            <th>Total Amount</th>
                            <th>Avg Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($claims_stats as $stat): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($stat['status']); ?></td>
                            <td><?php echo $stat['count']; ?></td>
                            <td>KSH <?php echo number_format($stat['total_amount'], 2); ?></td>
                            <td>KSH <?php echo number_format($stat['avg_amount'], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Payment Statistics -->
            <div class="stat-card">
                <h3>Payments by Status</h3>
                <button class="export-btn" onclick="exportTable('payments-table', 'payment_statistics.csv')">Export CSV</button>
                <table class="table" id="payments-table">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Count</th>
                            <th>Total Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($payments_stats as $stat): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($stat['status']); ?></td>
                            <td><?php echo $stat['count']; ?></td>
                            <td>KSH <?php echo number_format($stat['total_amount'], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Recent Users -->
            <div class="stat-card">
                <h3>Recent Users</h3>
                <button class="export-btn" onclick="exportTable('recent-users-table', 'recent_users.csv')">Export CSV</button>
                <table class="table" id="recent-users-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($recent_users as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                            <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function exportTable(tableId, filename) {
            const table = document.getElementById(tableId);
            let csv = [];
            
            // Get headers
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.trim());
            });
            csv.push(headers.join(','));
            
            // Get data rows
            table.querySelectorAll('tbody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    row.push('"' + td.textContent.trim().replace(/"/g, '""') + '"');
                });
                csv.push(row.join(','));
            });
            
            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
