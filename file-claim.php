<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

require_once "includes/db_config.php";

$customer_policy_id = $incident_date = $description = $claim_amount = $documents = "";
$customer_policy_id_err = $incident_date_err = $description_err = $claim_amount_err = $documents_err = "";
$success_message = $error_message = "";


$query = "SELECT cp.customer_policy_id, cp.policy_number, p.policy_name, pt.type_name, cp.start_date, cp.end_date, cp.total_premium, p.coverage_amount
          FROM customer_policies cp
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id AND cp.status = 'Active'
          ORDER BY cp.created_at DESC";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$active_policies = $stmt->fetchAll(PDO::FETCH_ASSOC);


if(isset($_GET["policy_id"]) && !empty($_GET["policy_id"])) {
    $customer_policy_id = $_GET["policy_id"];
}


if($_SERVER["REQUEST_METHOD"] == "POST") {


    if(empty(trim($_POST["customer_policy_id"]))) {
        $customer_policy_id_err = "Please select a policy.";
    } else {
        $customer_policy_id = trim($_POST["customer_policy_id"]);
    }


    if(empty(trim($_POST["incident_date"]))) {
        $incident_date_err = "Please enter the incident date.";
    } else {
        $incident_date = trim($_POST["incident_date"]);


        $policy_query = "SELECT start_date, end_date FROM customer_policies WHERE customer_policy_id = :customer_policy_id";
        $policy_stmt = $conn->prepare($policy_query);
        $policy_stmt->bindParam(":customer_policy_id", $customer_policy_id);
        $policy_stmt->execute();
        $policy = $policy_stmt->fetch(PDO::FETCH_ASSOC);

        if($incident_date < $policy["start_date"] || $incident_date > $policy["end_date"]) {
            $incident_date_err = "Incident date must be within the policy period (" . date('M d, Y', strtotime($policy["start_date"])) . " to " . date('M d, Y', strtotime($policy["end_date"])) . ").";
        }
    }


    if(empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description of the incident.";
    } elseif(strlen(trim($_POST["description"])) < 20) {
        $description_err = "Description must be at least 20 characters.";
    } else {
        $description = trim($_POST["description"]);
    }


    if(empty(trim($_POST["claim_amount"]))) {
        $claim_amount_err = "Please enter the claim amount.";
    } elseif(!is_numeric(trim($_POST["claim_amount"])) || trim($_POST["claim_amount"]) <= 0) {
        $claim_amount_err = "Please enter a valid claim amount.";
    } else {
        $claim_amount = trim($_POST["claim_amount"]);


        $coverage_query = "SELECT p.coverage_amount FROM customer_policies cp JOIN policies p ON cp.policy_id = p.policy_id WHERE cp.customer_policy_id = :customer_policy_id";
        $coverage_stmt = $conn->prepare($coverage_query);
        $coverage_stmt->bindParam(":customer_policy_id", $customer_policy_id);
        $coverage_stmt->execute();
        $coverage = $coverage_stmt->fetch(PDO::FETCH_ASSOC);

        if($claim_amount > $coverage["coverage_amount"]) {
            $claim_amount_err = "Claim amount cannot exceed the coverage amount (" . number_format($coverage["coverage_amount"], 2) . ").";
        }
    }


    if(!empty($_FILES["documents"]["name"])) {
        $target_dir = "uploads/claims/";


        if(!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }

        $file_name = basename($_FILES["documents"]["name"]);
        $target_file = $target_dir . time() . "_" . $file_name;
        $file_type = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));


        if($_FILES["documents"]["size"] > 5000000) {
            $documents_err = "File is too large. Maximum size is 5MB.";
        }


        $allowed_types = array("jpg", "jpeg", "png", "pdf", "doc", "docx");
        if(!in_array($file_type, $allowed_types)) {
            $documents_err = "Only JPG, JPEG, PNG, PDF, DOC, and DOCX files are allowed.";
        }


        if(empty($documents_err)) {
            if(move_uploaded_file($_FILES["documents"]["tmp_name"], $target_file)) {
                $documents = $target_file;
            } else {
                $documents_err = "There was an error uploading your file.";
            }
        }
    }


    if(empty($customer_policy_id_err) && empty($incident_date_err) && empty($description_err) && empty($claim_amount_err) && empty($documents_err)) {

        try {

            $conn->beginTransaction();


            $claim_number = "CLM-" . date("Ymd") . "-" . rand(1000, 9999);


            $sql = "INSERT INTO claims (customer_policy_id, claim_number, incident_date, description, claim_amount, status, documents)
                    VALUES (:customer_policy_id, :claim_number, :incident_date, :description, :claim_amount, 'Pending', :documents)";

            if($stmt = $conn->prepare($sql)) {

                $stmt->bindParam(":customer_policy_id", $param_customer_policy_id);
                $stmt->bindParam(":claim_number", $param_claim_number);
                $stmt->bindParam(":incident_date", $param_incident_date);
                $stmt->bindParam(":description", $param_description);
                $stmt->bindParam(":claim_amount", $param_claim_amount);
                $stmt->bindParam(":documents", $param_documents);


                $param_customer_policy_id = $customer_policy_id;
                $param_claim_number = $claim_number;
                $param_incident_date = $incident_date;
                $param_description = $description;
                $param_claim_amount = $claim_amount;
                $param_documents = $documents;


                if($stmt->execute()) {

                    $conn->commit();

                    $success_message = "Your claim has been submitted successfully. Claim Number: " . $claim_number;


                    $customer_policy_id = $incident_date = $description = $claim_amount = $documents = "";
                } else {
                    $error_message = "Something went wrong. Please try again later.";
                }
            }
        } catch(PDOException $e) {

            $conn->rollback();
            $error_message = "Error: " . $e->getMessage();
        }


        unset($stmt);
    }


    unset($conn);
}

$page_title = "File a Claim";
$page_description = "Submit a new insurance claim for your active policy.";

include_once 'includes/header.php';
?>

<section style="padding: 60px 0;">
    <div class="container">
        <h1 style="margin-bottom: 30px; color: #0056b3;">File a Claim</h1>

        <?php if(count($active_policies) > 0): ?>
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                <?php
                if(!empty($success_message)) {
                    echo '<div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724;">' . $success_message . '</div>';
                }

                if(!empty($error_message)) {
                    echo '<div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;">' . $error_message . '</div>';
                }
                ?>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                    <div style="margin-bottom: 20px;">
                        <label for="customer_policy_id" style="display: block; margin-bottom: 5px; font-weight: 500;">Select Policy *</label>
                        <select name="customer_policy_id" id="customer_policy_id" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                            <option value="">-- Select a Policy --</option>
                            <?php foreach($active_policies as $policy): ?>
                                <option value="<?php echo $policy['customer_policy_id']; ?>" <?php echo ($customer_policy_id == $policy['customer_policy_id']) ? 'selected' : ''; ?>>
                                    <?php echo $policy['policy_number'] . ' - ' . $policy['policy_name'] . ' (' . $policy['type_name'] . ')'; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $customer_policy_id_err; ?></span>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="incident_date" style="display: block; margin-bottom: 5px; font-weight: 500;">Incident Date *</label>
                        <input type="date" name="incident_date" id="incident_date" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $incident_date; ?>">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $incident_date_err; ?></span>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="description" style="display: block; margin-bottom: 5px; font-weight: 500;">Description of Incident *</label>
                        <textarea name="description" id="description" rows="5" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;"><?php echo $description; ?></textarea>
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $description_err; ?></span>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="claim_amount" style="display: block; margin-bottom: 5px; font-weight: 500;">Claim Amount (KSH) *</label>
                        <input type="number" name="claim_amount" id="claim_amount" step="0.01" min="0" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $claim_amount; ?>">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $claim_amount_err; ?></span>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="documents" style="display: block; margin-bottom: 5px; font-weight: 500;">Supporting Documents (Optional)</label>
                        <input type="file" name="documents" id="documents" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                        <small style="display: block; margin-top: 5px; color: #6c757d;">Upload supporting documents (photos, receipts, reports, etc.). Max size: 5MB. Allowed formats: JPG, JPEG, PNG, PDF, DOC, DOCX.</small>
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $documents_err; ?></span>
                    </div>

                    <div style="margin-top: 30px;">
                        <button type="submit" style="padding: 12px 25px; background-color: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Submit Claim</button>
                        <a href="my-claims.php" style="display: inline-block; margin-left: 10px; padding: 12px 25px; background-color: #6c757d; color: white; border: none; border-radius: 4px; text-decoration: none; font-size: 16px; font-weight: 500;">View My Claims</a>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; text-align: center;">
                <p style="margin-bottom: 20px; font-size: 18px;">You don't have any active policies to file a claim against.</p>
                <a href="browse_policies.php" style="display: inline-block; padding: 12px 25px; background-color: #0056b3; color: white; border: none; border-radius: 4px; text-decoration: none; font-size: 16px; font-weight: 500;">Explore Our Policies</a>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>