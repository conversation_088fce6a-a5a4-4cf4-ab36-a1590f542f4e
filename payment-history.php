<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "Payment History";
$page_description = "View your payment history and upcoming payments.";

// Include database connection
require_once 'includes/db_config.php';

// Get user's payments
$payments = array();
$query = "SELECT p.*, cp.policy_number, pol.policy_name, pt.type_name 
          FROM payments p
          JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
          JOIN policies pol ON cp.policy_id = pol.policy_id
          JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id
          ORDER BY p.payment_date DESC";

$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get upcoming payments
$upcoming_payments = array();
$query = "SELECT cp.*, p.policy_name, pt.type_name 
          FROM customer_policies cp
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id 
          AND cp.status = 'Active' 
          AND cp.next_payment_date IS NOT NULL
          ORDER BY cp.next_payment_date ASC";

$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$upcoming_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Payment History</h1>
        <p>View your payment history and upcoming payments.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">
            
            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>
                    
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>
                
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>
            
            <!-- Main Content -->
            <div>
                <!-- Upcoming Payments -->
                <h2 style="margin-bottom: 20px;">Upcoming Payments</h2>
                
                <?php if(count($upcoming_payments) > 0): ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Policy</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Type</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Due Date</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Amount</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($upcoming_payments as $payment): ?>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['policy_number']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['type_name']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($payment['next_payment_date'])); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <?php 
                                                // Calculate payment amount based on frequency
                                                $amount = $payment['total_premium'];
                                                switch($payment['payment_frequency']) {
                                                    case 'Monthly': $amount = $payment['total_premium'] / 12; break;
                                                    case 'Quarterly': $amount = $payment['total_premium'] / 4; break;
                                                    case 'Semi-Annually': $amount = $payment['total_premium'] / 2; break;
                                                }
                                                echo '$' . number_format($amount, 2);
                                            ?>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <a href="make-payment.php?policy_id=<?php echo $payment['customer_policy_id']; ?>" style="background-color: #28a745; color: white; padding: 5px 10px; border-radius: 4px; text-decoration: none; font-size: 14px;">Pay Now</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px; text-align: center;">
                        <p>You have no upcoming payments due.</p>
                    </div>
                <?php endif; ?>
                
                <!-- Payment History -->
                <h2 style="margin-bottom: 20px;">Payment History</h2>
                
                <?php if(count($payments) > 0): ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Payment #</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Policy</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Date</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Amount</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Method</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($payments as $payment): ?>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_number']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['policy_number']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">$<?php echo number_format($payment['amount'], 2); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <span style="background-color: 
                                                <?php 
                                                    switch($payment['status']) {
                                                        case 'Completed': echo '#28a745'; break;
                                                        case 'Pending': echo '#ffc107'; break;
                                                        case 'Failed': echo '#dc3545'; break;
                                                        case 'Refunded': echo '#17a2b8'; break;
                                                        default: echo '#6c757d';
                                                    }
                                                ?>; 
                                                color: <?php echo ($payment['status'] == 'Pending') ? '#212529' : 'white'; ?>; 
                                                padding: 3px 8px; 
                                                border-radius: 4px; 
                                                font-size: 12px;">
                                                <?php echo htmlspecialchars($payment['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px; text-align: center;">
                        <p>No payment records found.</p>
                    </div>
                <?php endif; ?>
                
                <!-- Payment Information -->
                <div style="background-color: #f0f5ff; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 15px; color: #0056b3;">Payment Information</h3>
                    <h4 style="margin-bottom: 10px;">Payment Methods</h4>
                    <ul style="padding-left: 20px; margin-bottom: 15px;">
                        <li style="margin-bottom: 5px;"><strong>Credit Card:</strong> Visa, MasterCard, American Express</li>
                        <li style="margin-bottom: 5px;"><strong>Debit Card:</strong> All major debit cards accepted</li>
                        <li style="margin-bottom: 5px;"><strong>Bank Transfer:</strong> Direct deposit to our bank account</li>
                        <li style="margin-bottom: 5px;"><strong>Mobile Money:</strong> M-Pesa, Airtel Money</li>
                        <li><strong>Cash:</strong> Payment at our office locations</li>
                    </ul>
                    
                    <h4 style="margin-bottom: 10px;">Payment Status Definitions</h4>
                    <ul style="padding-left: 20px;">
                        <li style="margin-bottom: 5px;"><strong style="color: #ffc107;">Pending:</strong> Payment is being processed</li>
                        <li style="margin-bottom: 5px;"><strong style="color: #28a745;">Completed:</strong> Payment has been successfully processed</li>
                        <li style="margin-bottom: 5px;"><strong style="color: #dc3545;">Failed:</strong> Payment was unsuccessful</li>
                        <li><strong style="color: #17a2b8;">Refunded:</strong> Payment was refunded to your account</li>
                    </ul>
                    
                    <p style="margin-top: 15px;">For any questions about payments, please contact our finance department.</p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Finance Department</a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
