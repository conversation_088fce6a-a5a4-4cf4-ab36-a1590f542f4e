<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle policy status updates
if(isset($_POST['update_status'])) {
    $policy_id = $_POST['policy_id'];
    $new_status = $_POST['status'];
    
    try {
        $stmt = $conn->prepare("UPDATE policies SET status = :status WHERE policy_id = :policy_id");
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':policy_id', $policy_id);
        
        if($stmt->execute()) {
            $success_message = "Policy status updated successfully!";
        } else {
            $error_message = "Error updating policy status.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all policies with their types and creator info
$query = "SELECT p.*, pt.type_name, 
                 CONCAT(u.first_name, ' ', u.last_name) as created_by_name,
                 COUNT(cp.customer_policy_id) as customer_count
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          LEFT JOIN users u ON p.created_by = u.user_id
          LEFT JOIN customer_policies cp ON p.policy_id = cp.policy_id
          GROUP BY p.policy_id
          ORDER BY p.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$policies = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Policies";
$page_description = "View and manage all insurance policies.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 12px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
        .status-discontinued { background-color: #fff3cd; color: #856404; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .search-box { margin-bottom: 20px; }
        .search-box input { padding: 10px; border: 1px solid #ddd; border-radius: 4px; width: 300px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #28a745; }
        .stat-number { font-size: 24px; font-weight: bold; color: #28a745; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <div class="content">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- Policy Statistics -->
            <div class="stats-grid">
                <?php
                $total_policies = count($policies);
                $active_policies = count(array_filter($policies, function($p) { return $p['status'] == 'Active'; }));
                $total_customers = array_sum(array_column($policies, 'customer_count'));
                $total_premiums = array_sum(array_column($policies, 'premium_amount'));
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_policies; ?></div>
                    <div class="stat-label">Total Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $active_policies; ?></div>
                    <div class="stat-label">Active Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_customers; ?></div>
                    <div class="stat-label">Total Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSH <?php echo number_format($total_premiums, 0); ?></div>
                    <div class="stat-label">Total Premiums</div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>All Policies (<?php echo count($policies); ?>)</h2>
                <a href="create_policy.php" class="btn btn-success">Create New Policy</a>
            </div>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Search policies..." onkeyup="searchPolicies()">
            </div>

            <table class="table" id="policiesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Policy Name</th>
                        <th>Type</th>
                        <th>Premium</th>
                        <th>Coverage</th>
                        <th>Duration</th>
                        <th>Customers</th>
                        <th>Status</th>
                        <th>Created By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($policies as $policy): ?>
                    <tr>
                        <td><?php echo $policy['policy_id']; ?></td>
                        <td><strong><?php echo htmlspecialchars($policy['policy_name']); ?></strong></td>
                        <td><?php echo htmlspecialchars($policy['type_name']); ?></td>
                        <td>KSH <?php echo number_format($policy['premium_amount'], 2); ?></td>
                        <td>KSH <?php echo number_format($policy['coverage_amount'], 2); ?></td>
                        <td><?php echo htmlspecialchars($policy['duration'] ?: 'N/A'); ?></td>
                        <td><?php echo $policy['customer_count']; ?></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                                <?php echo $policy['status']; ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($policy['created_by_name'] ?: 'Unknown'); ?></td>
                        <td>
                            <a href="view_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-primary">View</a>
                            <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-success">Edit</a>
                            
                            <!-- Quick status update -->
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="policy_id" value="<?php echo $policy['policy_id']; ?>">
                                <select name="status" onchange="this.form.submit()" style="padding: 4px; font-size: 12px;">
                                    <option value="Active" <?php echo ($policy['status'] == 'Active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="Inactive" <?php echo ($policy['status'] == 'Inactive') ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="Discontinued" <?php echo ($policy['status'] == 'Discontinued') ? 'selected' : ''; ?>>Discontinued</option>
                                </select>
                                <input type="hidden" name="update_status" value="1">
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if(empty($policies)): ?>
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No policies found</h3>
                    <p>Start by creating your first insurance policy.</p>
                    <a href="create_policy.php" class="btn btn-success" style="margin-top: 15px;">Create First Policy</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function searchPolicies() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('policiesTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length - 1; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                rows[i].style.display = found ? '' : 'none';
            }
        }
    </script>
</body>
</html>
