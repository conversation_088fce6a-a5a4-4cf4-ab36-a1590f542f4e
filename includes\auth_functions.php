<?php
/**
 * Authentication and Authorization Functions
 */

/**
 * Check if the user has a specific role
 * 
 * @param string $role_name The role name to check
 * @return bool True if the user has the role, false otherwise
 */
function has_role($role_name) {
    // Check if user is logged in
    if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
        return false;
    }
    
    // Check if user has roles
    if (!isset($_SESSION["roles"]) || empty($_SESSION["roles"])) {
        return false;
    }
    
    // Check if user has the specific role
    foreach ($_SESSION["roles"] as $role) {
        if ($role["role_name"] === $role_name) {
            return true;
        }
    }
    
    return false;
}

/**
 * Require a specific role to access a page
 * 
 * @param string|array $required_roles The role(s) required to access the page
 * @param string $redirect_url The URL to redirect to if the user doesn't have the required role
 * @return void
 */
function require_role($required_roles, $redirect_url = "login.php") {
    // Check if user is logged in
    if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
        header("location: " . $redirect_url);
        exit;
    }
    
    // Convert single role to array
    if (!is_array($required_roles)) {
        $required_roles = [$required_roles];
    }
    
    // Check if user has any of the required roles
    $has_required_role = false;
    foreach ($required_roles as $role) {
        if (has_role($role)) {
            $has_required_role = true;
            break;
        }
    }
    
    // Redirect if user doesn't have any of the required roles
    if (!$has_required_role) {
        header("location: " . $redirect_url);
        exit;
    }
}

/**
 * Get the dashboard URL for a specific role
 * 
 * @param string $role_name The role name
 * @return string The dashboard URL
 */
function get_dashboard_url($role_name) {
    switch ($role_name) {
        case "Administrator":
            return "admin/dashboard.php";
        case "Insurance Agent":
            return "agent/dashboard.php";
        case "System Accountant":
            return "accountant/dashboard.php";
        case "Customer":
        default:
            return "dashboard.php";
    }
}
?>
