<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($invoice_id <= 0) {
    header("location: invoices.php");
    exit;
}

// Get invoice details for printing
$query = "SELECT i.*, cp.policy_number, p.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email, customer.phone as customer_phone,
                 customer.address as customer_address
          FROM invoices i
          JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          WHERE i.invoice_id = :invoice_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':invoice_id', $invoice_id);
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$invoice) {
    header("location: invoices.php");
    exit;
}

$page_title = "Invoice - " . $invoice['invoice_number'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: white; color: #333; line-height: 1.6; }
        .invoice-container { max-width: 800px; margin: 20px auto; padding: 40px; border: 2px solid #ddd; }
        .company-header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid #6f42c1; padding-bottom: 20px; }
        .company-name { font-size: 32px; font-weight: bold; color: #6f42c1; margin-bottom: 5px; }
        .company-tagline { font-size: 16px; color: #6c757d; margin-bottom: 10px; }
        .company-details { font-size: 14px; color: #6c757d; }
        .invoice-title { text-align: center; font-size: 28px; font-weight: bold; color: #6f42c1; margin: 40px 0; }
        .invoice-header { display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px; }
        .bill-to, .invoice-details { }
        .section-title { font-size: 18px; font-weight: bold; color: #6f42c1; margin-bottom: 15px; border-bottom: 2px solid #6f42c1; padding-bottom: 5px; }
        .info-item { margin-bottom: 8px; display: flex; }
        .info-label { font-weight: 600; min-width: 120px; color: #6c757d; }
        .info-value { flex: 1; }
        .invoice-table { width: 100%; border-collapse: collapse; margin: 40px 0; }
        .invoice-table th, .invoice-table td { padding: 15px; text-align: left; border-bottom: 1px solid #ddd; }
        .invoice-table th { background-color: #6f42c1; color: white; font-weight: 600; }
        .amount-section { text-align: right; margin: 40px 0; }
        .total-amount { font-size: 24px; font-weight: bold; color: #6f42c1; padding: 15px; background-color: #f8f9fa; border-radius: 8px; }
        .payment-terms { margin: 40px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px; }
        .footer { margin-top: 60px; text-align: center; font-size: 12px; color: #6c757d; border-top: 1px solid #ddd; padding-top: 20px; }
        .print-btn { background-color: #6f42c1; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 20px auto; display: block; }
        .print-btn:hover { background-color: #5a2d91; }
        .status-badge { padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; display: inline-block; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-paid { background-color: #d4edda; color: #155724; }
        .status-overdue { background-color: #f8d7da; color: #721c24; }
        .watermark { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); font-size: 120px; color: rgba(111, 66, 193, 0.05); z-index: -1; pointer-events: none; }
        
        @media print {
            body { background-color: white; }
            .print-btn { display: none; }
            .invoice-container { border: none; margin: 0; padding: 20px; }
            .watermark { display: none; }
        }
    </style>
</head>
<body>
    <div class="watermark">ZAMARA</div>
    
    <div class="invoice-container">
        <!-- Company Header -->
        <div class="company-header">
            <div class="company-name">ZAMARA INSURANCE LIMITED</div>
            <div class="company-tagline">Your Trusted Insurance Partner</div>
            <div class="company-details">
                P.O. Box 12345, Nairobi, Kenya<br>
                Tel: +*********** 456 | Email: <EMAIL><br>
                Website: www.zamara.co.ke | PIN: P051234567A
            </div>
        </div>

        <!-- Invoice Title -->
        <div class="invoice-title">INVOICE</div>

        <!-- Invoice Header -->
        <div class="invoice-header">
            <!-- Bill To -->
            <div class="bill-to">
                <div class="section-title">Bill To</div>
                <div class="info-item">
                    <span class="info-label">Customer:</span>
                    <span class="info-value"><?php echo htmlspecialchars($invoice['customer_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($invoice['customer_email']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phone:</span>
                    <span class="info-value"><?php echo htmlspecialchars($invoice['customer_phone'] ?: 'N/A'); ?></span>
                </div>
                <?php if($invoice['customer_address']): ?>
                <div class="info-item">
                    <span class="info-label">Address:</span>
                    <span class="info-value"><?php echo htmlspecialchars($invoice['customer_address']); ?></span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Invoice Details -->
            <div class="invoice-details">
                <div class="section-title">Invoice Details</div>
                <div class="info-item">
                    <span class="info-label">Invoice #:</span>
                    <span class="info-value"><?php echo htmlspecialchars($invoice['invoice_number']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Invoice Date:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($invoice['created_at'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Due Date:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($invoice['due_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo strtolower($invoice['status']); ?>">
                            <?php echo $invoice['status']; ?>
                        </span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Invoice Items Table -->
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Policy Details</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <strong><?php echo htmlspecialchars($invoice['description'] ?: 'Insurance Premium Payment'); ?></strong><br>
                        <small>Policy: <?php echo htmlspecialchars($invoice['policy_name']); ?></small>
                    </td>
                    <td>
                        Policy Number: <?php echo htmlspecialchars($invoice['policy_number']); ?><br>
                        Policy Type: <?php echo htmlspecialchars($invoice['type_name']); ?><br>
                        Billing Period: <?php echo date('M Y', strtotime($invoice['created_at'])); ?>
                    </td>
                    <td style="text-align: right;">
                        <strong>KSH <?php echo number_format($invoice['amount'], 2); ?></strong>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Amount Section -->
        <div class="amount-section">
            <div style="margin-bottom: 10px;">
                <span style="display: inline-block; width: 150px; text-align: left;">Subtotal:</span>
                <span>KSH <?php echo number_format($invoice['amount'], 2); ?></span>
            </div>
            <div style="margin-bottom: 10px;">
                <span style="display: inline-block; width: 150px; text-align: left;">Tax (0%):</span>
                <span>KSH 0.00</span>
            </div>
            <div class="total-amount">
                <span style="display: inline-block; width: 150px; text-align: left;">Total Amount:</span>
                <span>KSH <?php echo number_format($invoice['amount'], 2); ?></span>
            </div>
            <div style="margin-top: 10px; font-size: 14px; color: #6c757d;">
                (<?php echo ucwords(convertNumberToWords($invoice['amount'])); ?> Shillings Only)
            </div>
        </div>

        <!-- Payment Terms -->
        <div class="payment-terms">
            <div class="section-title">Payment Terms & Instructions</div>
            <p><strong>Payment Due Date:</strong> <?php echo date('M d, Y', strtotime($invoice['due_date'])); ?></p>
            <p><strong>Payment Methods:</strong></p>
            <ul style="margin: 10px 0 10px 20px;">
                <li><strong>M-Pesa:</strong> Pay Bill 123456, Account: <?php echo $invoice['invoice_number']; ?></li>
                <li><strong>Bank Transfer:</strong> Account Name: Zamara Insurance Ltd, Account: **********</li>
                <li><strong>Online:</strong> Visit www.zamara.co.ke/pay</li>
            </ul>
            <p><strong>Note:</strong> Please include your invoice number as reference when making payment.</p>
            
            <?php if($invoice['status'] == 'Pending'): ?>
                <?php
                $due_date = strtotime($invoice['due_date']);
                $today = strtotime(date('Y-m-d'));
                $days_diff = ($due_date - $today) / (60 * 60 * 24);
                
                if($days_diff < 0) {
                    echo '<p style="color: #dc3545; font-weight: bold; margin-top: 15px;">⚠️ This invoice is OVERDUE by ' . abs(floor($days_diff)) . ' days. Please pay immediately to avoid late fees.</p>';
                } elseif($days_diff <= 7) {
                    echo '<p style="color: #ffc107; font-weight: bold; margin-top: 15px;">⚠️ This invoice is due in ' . floor($days_diff) . ' days.</p>';
                }
                ?>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Thank you for choosing Zamara Insurance!</strong></p>
            <p>This invoice was generated electronically and is valid without signature.</p>
            <p>For any queries regarding this invoice, please contact <NAME_EMAIL> or +*********** 456</p>
            <p style="margin-top: 15px;">Generated on: <?php echo date('M d, Y H:i:s'); ?></p>
        </div>

        <!-- Print Button -->
        <button class="print-btn" onclick="window.print()">Print Invoice</button>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>

<?php
// Function to convert number to words (same as in receipt.php)
function convertNumberToWords($number) {
    $ones = array(
        0 => 'zero', 1 => 'one', 2 => 'two', 3 => 'three', 4 => 'four', 5 => 'five',
        6 => 'six', 7 => 'seven', 8 => 'eight', 9 => 'nine', 10 => 'ten',
        11 => 'eleven', 12 => 'twelve', 13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
        16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen', 19 => 'nineteen'
    );
    
    $tens = array(
        2 => 'twenty', 3 => 'thirty', 4 => 'forty', 5 => 'fifty',
        6 => 'sixty', 7 => 'seventy', 8 => 'eighty', 9 => 'ninety'
    );
    
    if ($number < 20) {
        return $ones[$number];
    } elseif ($number < 100) {
        return $tens[intval($number / 10)] . ($number % 10 != 0 ? ' ' . $ones[$number % 10] : '');
    } elseif ($number < 1000) {
        return $ones[intval($number / 100)] . ' hundred' . ($number % 100 != 0 ? ' ' . convertNumberToWords($number % 100) : '');
    } elseif ($number < 1000000) {
        return convertNumberToWords(intval($number / 1000)) . ' thousand' . ($number % 1000 != 0 ? ' ' . convertNumberToWords($number % 1000) : '');
    } else {
        return convertNumberToWords(intval($number / 1000000)) . ' million' . ($number % 1000000 != 0 ? ' ' . convertNumberToWords($number % 1000000) : '');
    }
}
?>
