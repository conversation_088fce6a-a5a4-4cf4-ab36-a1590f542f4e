<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$policy_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($policy_id <= 0) {
    header("location: policies.php");
    exit;
}

// Get policy details
$query = "SELECT p.*, pt.type_name, 
                 CONCAT(creator.first_name, ' ', creator.last_name) as created_by_name,
                 COUNT(cp.customer_policy_id) as assigned_count
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          LEFT JOIN users creator ON p.created_by = creator.user_id
          LEFT JOIN customer_policies cp ON p.policy_id = cp.policy_id
          WHERE p.policy_id = :policy_id
          GROUP BY p.policy_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':policy_id', $policy_id);
$stmt->execute();
$policy = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$policy) {
    header("location: policies.php");
    exit;
}

$page_title = "Policy Details - " . $policy['policy_name'];
$page_description = "View detailed policy information.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .policy-header { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: start; margin-bottom: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .status-badge { padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
        .status-discontinued { background-color: #e2e3e5; color: #383d41; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; }
        .info-section h4 { color: #28a745; margin-bottom: 15px; border-bottom: 2px solid #28a745; padding-bottom: 5px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: 600; color: #6c757d; font-size: 12px; margin-bottom: 3px; }
        .info-value { font-size: 14px; }
        .amount-display { font-size: 24px; font-weight: bold; color: #28a745; text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="policies.php" class="back-link">← Back to Policies</a>
        
        <!-- Policy Header -->
        <div class="content">
            <div class="policy-header">
                <div>
                    <h2><?php echo htmlspecialchars($policy['policy_name']); ?></h2>
                    <p style="color: #6c757d; margin-bottom: 10px;">
                        Policy ID: <?php echo $policy['policy_id']; ?> | 
                        Type: <?php echo htmlspecialchars($policy['type_name']); ?>
                    </p>
                    <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                        <?php echo $policy['status']; ?>
                    </span>
                </div>
                <div style="display: flex; gap: 10px; flex-direction: column;">
                    <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-primary">Edit Policy</a>
                    <a href="assign_policy.php?policy_id=<?php echo $policy['policy_id']; ?>" class="btn btn-success">Assign to Customer</a>
                    <?php if($policy['status'] == 'Active'): ?>
                        <button onclick="deactivatePolicy()" class="btn btn-warning">Deactivate</button>
                    <?php else: ?>
                        <button onclick="activatePolicy()" class="btn btn-success">Activate</button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Premium and Coverage -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="amount-display">
                    Premium: KSH <?php echo number_format($policy['premium_amount'], 2); ?>
                </div>
                <div class="amount-display">
                    Coverage: KSH <?php echo number_format($policy['coverage_amount'], 2); ?>
                </div>
            </div>
        </div>

        <!-- Policy Details -->
        <div class="info-grid">
            <!-- Basic Information -->
            <div class="info-section">
                <h4>Basic Information</h4>
                <div class="info-item">
                    <div class="info-label">POLICY NAME</div>
                    <div class="info-value"><?php echo htmlspecialchars($policy['policy_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">POLICY TYPE</div>
                    <div class="info-value"><?php echo htmlspecialchars($policy['type_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">DURATION</div>
                    <div class="info-value"><?php echo htmlspecialchars($policy['duration']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">STATUS</div>
                    <div class="info-value">
                        <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                            <?php echo $policy['status']; ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="info-section">
                <h4>Financial Information</h4>
                <div class="info-item">
                    <div class="info-label">PREMIUM AMOUNT</div>
                    <div class="info-value"><strong>KSH <?php echo number_format($policy['premium_amount'], 2); ?></strong></div>
                </div>
                <div class="info-item">
                    <div class="info-label">COVERAGE AMOUNT</div>
                    <div class="info-value"><strong>KSH <?php echo number_format($policy['coverage_amount'], 2); ?></strong></div>
                </div>
                <div class="info-item">
                    <div class="info-label">COVERAGE RATIO</div>
                    <div class="info-value"><?php echo number_format(($policy['coverage_amount'] / $policy['premium_amount']), 1); ?>x</div>
                </div>
            </div>

            <!-- Management Information -->
            <div class="info-section">
                <h4>Management Information</h4>
                <div class="info-item">
                    <div class="info-label">CREATED BY</div>
                    <div class="info-value"><?php echo htmlspecialchars($policy['created_by_name'] ?: 'System'); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">CREATED ON</div>
                    <div class="info-value"><?php echo date('M d, Y H:i', strtotime($policy['created_at'])); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">LAST UPDATED</div>
                    <div class="info-value"><?php echo date('M d, Y H:i', strtotime($policy['updated_at'])); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">ASSIGNED TO CUSTOMERS</div>
                    <div class="info-value"><strong><?php echo $policy['assigned_count']; ?></strong> customers</div>
                </div>
            </div>
        </div>

        <!-- Policy Description -->
        <?php if($policy['description']): ?>
        <div class="content">
            <h4>Policy Description</h4>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
                <?php echo nl2br(htmlspecialchars($policy['description'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Terms and Conditions -->
        <?php if($policy['terms_conditions']): ?>
        <div class="content">
            <h4>Terms and Conditions</h4>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px; line-height: 1.6;">
                <?php echo nl2br(htmlspecialchars($policy['terms_conditions'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="content">
            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <a href="policies.php" class="btn btn-secondary">Back to Policies</a>
                <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-primary">Edit Policy</a>
                <a href="assign_policy.php?policy_id=<?php echo $policy['policy_id']; ?>" class="btn btn-success">Assign to Customer</a>
                <?php if($policy['status'] == 'Active'): ?>
                    <button onclick="deactivatePolicy()" class="btn btn-warning">Deactivate Policy</button>
                <?php else: ?>
                    <button onclick="activatePolicy()" class="btn btn-success">Activate Policy</button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function activatePolicy() {
            if(confirm('Activate this policy?')) {
                // In a real system, this would make an AJAX call
                alert('Policy activated successfully!');
                location.reload();
            }
        }

        function deactivatePolicy() {
            const reason = prompt('Enter reason for deactivating this policy:');
            if(reason) {
                if(confirm('Deactivate this policy?')) {
                    alert('Policy deactivated successfully.');
                    location.reload();
                }
            }
        }
    </script>
</body>
</html>
