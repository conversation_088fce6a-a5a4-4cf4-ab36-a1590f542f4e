<?php
// Fix user passwords in the database
echo "<h2>🔧 Password Fix Script</h2>";

require_once 'includes/db_config.php';

if (!$conn) {
    die("❌ Database connection failed");
}

// Define the correct passwords
$users_passwords = [
    'admin' => 'admin123',
    'agent1' => 'agent123', 
    'accountant1' => 'accountant123',
    'customer1' => 'customer123'
];

echo "<h3>Updating passwords...</h3>";

try {
    foreach ($users_passwords as $username => $password) {
        // Generate proper password hash
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Update the user's password
        $stmt = $conn->prepare("UPDATE users SET password = :password WHERE username = :username");
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':username', $username);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Updated password for user: <strong>$username</strong></p>";
            
            // Verify the password works
            $verify_stmt = $conn->prepare("SELECT password FROM users WHERE username = :username");
            $verify_stmt->bindParam(':username', $username);
            $verify_stmt->execute();
            $stored_hash = $verify_stmt->fetch(PDO::FETCH_ASSOC)['password'];
            
            if (password_verify($password, $stored_hash)) {
                echo "<p style='color: green;'>   ✅ Password verification: SUCCESS</p>";
            } else {
                echo "<p style='color: red;'>   ❌ Password verification: FAILED</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to update password for user: $username</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 Password Update Complete!</h3>";
    echo "<p><strong>You can now login with these credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Administrator:</strong> username = <code>admin</code>, password = <code>admin123</code></li>";
    echo "<li><strong>Insurance Agent:</strong> username = <code>agent1</code>, password = <code>agent123</code></li>";
    echo "<li><strong>System Accountant:</strong> username = <code>accountant1</code>, password = <code>accountant123</code></li>";
    echo "<li><strong>Customer:</strong> username = <code>customer1</code>, password = <code>customer123</code></li>";
    echo "</ul>";
    echo "<p><a href='login.php' style='background-color: #0056b3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
