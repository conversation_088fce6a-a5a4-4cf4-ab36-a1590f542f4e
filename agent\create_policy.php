<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_agent = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Insurance Agent") {
        $is_agent = true;
        break;
    }
}

if(!$is_agent) {
    header("location: ../dashboard.php");
    exit;
}

$page_title = "Create New Policy";
$page_description = "Create a new insurance policy for customers.";

require_once '../includes/db_config.php';

$policy_name = $policy_type_id = $description = $duration = $premium_amount = $coverage_amount = $terms_conditions = "";
$policy_name_err = $policy_type_id_err = $description_err = $duration_err = $premium_amount_err = $coverage_amount_err = "";
$success_message = "";


$query = "SELECT policy_type_id, type_name FROM policy_types ORDER BY type_name";
$stmt = $conn->prepare($query);
$stmt->execute();
$policy_types = $stmt->fetchAll(PDO::FETCH_ASSOC);


if($_SERVER["REQUEST_METHOD"] == "POST") {


    if(empty(trim($_POST["policy_name"]))) {
        $policy_name_err = "Please enter a policy name.";
    } else {
        $policy_name = trim($_POST["policy_name"]);
    }


    if(empty(trim($_POST["policy_type_id"]))) {
        $policy_type_id_err = "Please select a policy type.";
    } else {
        $policy_type_id = trim($_POST["policy_type_id"]);
    }


    if(empty(trim($_POST["description"]))) {
        $description_err = "Please enter a description.";
    } else {
        $description = trim($_POST["description"]);
    }


    if(empty(trim($_POST["duration"]))) {
        $duration_err = "Please enter the policy duration.";
    } else {
        $duration = trim($_POST["duration"]);
    }


    if(empty(trim($_POST["premium_amount"]))) {
        $premium_amount_err = "Please enter the premium amount.";
    } elseif(!is_numeric(trim($_POST["premium_amount"])) || trim($_POST["premium_amount"]) <= 0) {
        $premium_amount_err = "Please enter a valid premium amount.";
    } else {
        $premium_amount = trim($_POST["premium_amount"]);
    }


    if(empty(trim($_POST["coverage_amount"]))) {
        $coverage_amount_err = "Please enter the coverage amount.";
    } elseif(!is_numeric(trim($_POST["coverage_amount"])) || trim($_POST["coverage_amount"]) <= 0) {
        $coverage_amount_err = "Please enter a valid coverage amount.";
    } else {
        $coverage_amount = trim($_POST["coverage_amount"]);
    }


    $terms_conditions = trim($_POST["terms_conditions"]);


    if(empty($policy_name_err) && empty($policy_type_id_err) && empty($description_err) &&
       empty($duration_err) && empty($premium_amount_err) && empty($coverage_amount_err)) {


        $query = "INSERT INTO policies (policy_name, policy_type_id, description, duration, premium_amount, coverage_amount, terms_conditions, created_by)
                  VALUES (:policy_name, :policy_type_id, :description, :duration, :premium_amount, :coverage_amount, :terms_conditions, :created_by)";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(":policy_name", $policy_name);
        $stmt->bindParam(":policy_type_id", $policy_type_id);
        $stmt->bindParam(":description", $description);
        $stmt->bindParam(":duration", $duration);
        $stmt->bindParam(":premium_amount", $premium_amount);
        $stmt->bindParam(":coverage_amount", $coverage_amount);
        $stmt->bindParam(":terms_conditions", $terms_conditions);
        $stmt->bindParam(":created_by", $_SESSION["user_id"]);

        if($stmt->execute()) {
            $success_message = "Policy has been created successfully.";


            $policy_name = $policy_type_id = $description = $duration = $premium_amount = $coverage_amount = $terms_conditions = "";
        } else {
            echo "Something went wrong. Please try again later.";
        }
    }
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Zamara' : 'Zamara - Insurance & Financial Services'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Zamara offers comprehensive insurance and financial services to individuals and businesses.'; ?>">

    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .agent-header {
            background-color: #28a745;
            color: white;
        }

        .agent-sidebar {
            background-color: #f8f9fa;
            min-height: calc(100vh - 70px);
            border-right: 1px solid #ddd;
        }

        .agent-sidebar a {
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }

        .agent-sidebar a:hover, .agent-sidebar a.active {
            background-color: #28a745;
            color: white;
        }

        .agent-content {
            padding: 20px;
        }
    </style>
</head>
<body>

    <header class="agent-header">
        <div class="container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
            <div>
                <h2>Zamara Insurance Agent</h2>
            </div>

            <div style="display: flex; align-items: center;">
                <span style="margin-right: 15px;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                <a href="../logout.php" style="color: white; text-decoration: none; background-color: rgba(0,0,0,0.2); padding: 5px 10px; border-radius: 4px;">Logout</a>
            </div>
        </div>
    </header>

    <div style="display: flex;">

        <div class="agent-sidebar" style="width: 250px; padding: 20px;">
            <h3 style="margin-bottom: 20px;">Agent Menu</h3>

            <nav>
                <a href="dashboard.php">Dashboard</a>
                <a href="policies.php">Manage Policies</a>
                <a href="create_policy.php" class="active">Create New Policy</a>
                <a href="customers.php">Manage Customers</a>
                <a href="claims.php">Manage Claims</a>
                <a href="../index.php">View Website</a>
            </nav>
        </div>


        <div class="agent-content" style="flex: 1;">
            <h1 style="margin-bottom: 20px;">Create New Policy</h1>

            <?php if(!empty($success_message)): ?>
                <div style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 30px;">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div style="margin-bottom: 20px;">
                        <label for="policy_name" style="display: block; margin-bottom: 5px; font-weight: 500;">Policy Name *</label>
                        <input type="text" id="policy_name" name="policy_name" value="<?php echo $policy_name; ?>" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($policy_name_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                        <?php if(!empty($policy_name_err)): ?>
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $policy_name_err; ?></span>
                        <?php endif; ?>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="policy_type_id" style="display: block; margin-bottom: 5px; font-weight: 500;">Policy Type *</label>
                        <select id="policy_type_id" name="policy_type_id" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($policy_type_id_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                            <option value="">-- Select Policy Type --</option>
                            <?php foreach($policy_types as $type): ?>
                                <option value="<?php echo $type['policy_type_id']; ?>" <?php echo ($policy_type_id == $type['policy_type_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if(!empty($policy_type_id_err)): ?>
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $policy_type_id_err; ?></span>
                        <?php endif; ?>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="description" style="display: block; margin-bottom: 5px; font-weight: 500;">Description *</label>
                        <textarea id="description" name="description" rows="4" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($description_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;"><?php echo $description; ?></textarea>
                        <?php if(!empty($description_err)): ?>
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $description_err; ?></span>
                        <?php endif; ?>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="duration" style="display: block; margin-bottom: 5px; font-weight: 500;">Duration *</label>
                        <input type="text" id="duration" name="duration" value="<?php echo $duration; ?>" placeholder="e.g., 1 Year, 6 Months" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($duration_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                        <?php if(!empty($duration_err)): ?>
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $duration_err; ?></span>
                        <?php endif; ?>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="premium_amount" style="display: block; margin-bottom: 5px; font-weight: 500;">Premium Amount ($) *</label>
                            <input type="number" id="premium_amount" name="premium_amount" value="<?php echo $premium_amount; ?>" min="0" step="0.01" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($premium_amount_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                            <?php if(!empty($premium_amount_err)): ?>
                                <span style="color: #dc3545; font-size: 14px;"><?php echo $premium_amount_err; ?></span>
                            <?php endif; ?>
                        </div>

                        <div>
                            <label for="coverage_amount" style="display: block; margin-bottom: 5px; font-weight: 500;">Coverage Amount ($) *</label>
                            <input type="number" id="coverage_amount" name="coverage_amount" value="<?php echo $coverage_amount; ?>" min="0" step="0.01" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($coverage_amount_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                            <?php if(!empty($coverage_amount_err)): ?>
                                <span style="color: #dc3545; font-size: 14px;"><?php echo $coverage_amount_err; ?></span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="terms_conditions" style="display: block; margin-bottom: 5px; font-weight: 500;">Terms & Conditions</label>
                        <textarea id="terms_conditions" name="terms_conditions" rows="6" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"><?php echo $terms_conditions; ?></textarea>
                    </div>

                    <div>
                        <button type="submit" style="background-color: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500;">Create Policy</button>
                        <a href="policies.php" style="display: inline-block; margin-left: 10px; padding: 12px 30px; background-color: #6c757d; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <footer style="background-color: #f8f9fa; color: #333; padding: 20px 0; text-align: center; margin-top: 30px; border-top: 1px solid #ddd;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Zamara Insurance. All Rights Reserved.</p>
        </div>
    </footer>


    <script src="../assets/js/main.js"></script>
</body>
</html>
