<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle payment recording
if($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['record_payment'])) {
    $customer_policy_id = $_POST['customer_policy_id'];
    $amount = $_POST['amount'];
    $payment_method = $_POST['payment_method'];
    $transaction_reference = trim($_POST['transaction_reference']);
    $payment_date = $_POST['payment_date'];
    $notes = trim($_POST['notes']);
    
    try {
        // Generate payment number
        $payment_number = 'PAY-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        
        // Check if payment number already exists
        $check_stmt = $conn->prepare("SELECT COUNT(*) FROM payments WHERE payment_number = :payment_number");
        $check_stmt->bindParam(':payment_number', $payment_number);
        $check_stmt->execute();
        
        while($check_stmt->fetchColumn() > 0) {
            $payment_number = 'PAY-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
            $check_stmt->bindParam(':payment_number', $payment_number);
            $check_stmt->execute();
        }
        
        $stmt = $conn->prepare("INSERT INTO payments (payment_number, customer_policy_id, amount, payment_method, transaction_reference, payment_date, status, notes, recorded_by) VALUES (:payment_number, :customer_policy_id, :amount, :payment_method, :transaction_reference, :payment_date, 'Completed', :notes, :recorded_by)");
        $stmt->bindParam(':payment_number', $payment_number);
        $stmt->bindParam(':customer_policy_id', $customer_policy_id);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':payment_method', $payment_method);
        $stmt->bindParam(':transaction_reference', $transaction_reference);
        $stmt->bindParam(':payment_date', $payment_date);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':recorded_by', $_SESSION['user_id']);
        
        if($stmt->execute()) {
            $success_message = "Payment recorded successfully! Payment #: $payment_number";
        } else {
            $error_message = "Error recording payment.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get customer policies for payment recording
$policies_query = "SELECT cp.customer_policy_id, cp.policy_number, p.policy_name, pt.type_name,
                          CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                          u.email as customer_email, p.premium_amount
                   FROM customer_policies cp
                   JOIN policies p ON cp.policy_id = p.policy_id
                   JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                   JOIN users u ON cp.customer_id = u.user_id
                   WHERE cp.status = 'Active'
                   ORDER BY u.first_name, u.last_name, p.policy_name";
$policies_stmt = $conn->prepare($policies_query);
$policies_stmt->execute();
$customer_policies = $policies_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Record Payment";
$page_description = "Record a new payment transaction.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #343a40; color: white; padding: 20px 0; margin-bottom: 30px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #343a40; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #23272b; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #343a40; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .error { color: #dc3545; font-size: 14px; margin-top: 5px; }
        .policy-info { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px; display: none; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .info-item { }
        .info-label { font-weight: 600; color: #6c757d; font-size: 12px; }
        .info-value { font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="payments.php" class="back-link">← Back to Payments</a>
        
        <div class="form-container">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="form-group">
                    <label>Customer Policy *</label>
                    <select name="customer_policy_id" required onchange="showPolicyInfo(this)">
                        <option value="">Select Customer Policy</option>
                        <?php foreach($customer_policies as $cp): ?>
                            <option value="<?php echo $cp['customer_policy_id']; ?>" 
                                    data-customer="<?php echo htmlspecialchars($cp['customer_name']); ?>"
                                    data-email="<?php echo htmlspecialchars($cp['customer_email']); ?>"
                                    data-policy="<?php echo htmlspecialchars($cp['policy_name']); ?>"
                                    data-type="<?php echo htmlspecialchars($cp['type_name']); ?>"
                                    data-premium="<?php echo $cp['premium_amount']; ?>">
                                <?php echo htmlspecialchars($cp['customer_name'] . ' - ' . $cp['policy_name'] . ' (' . $cp['policy_number'] . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <div id="policyInfo" class="policy-info">
                        <h4>Policy Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">CUSTOMER</div>
                                <div class="info-value" id="customerName"></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">EMAIL</div>
                                <div class="info-value" id="customerEmail"></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">POLICY</div>
                                <div class="info-value" id="policyName"></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">TYPE</div>
                                <div class="info-value" id="policyType"></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">PREMIUM</div>
                                <div class="info-value" id="premiumAmount"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Payment Amount (KSH) *</label>
                        <input type="number" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>Payment Method *</label>
                        <select name="payment_method" required>
                            <option value="">Select Payment Method</option>
                            <option value="Mpesa">M-Pesa</option>
                            <option value="Bank Transfer">Bank Transfer</option>
                            <option value="Credit Card">Credit Card</option>
                            <option value="Debit Card">Debit Card</option>
                            <option value="Cash">Cash</option>
                            <option value="Cheque">Cheque</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Transaction Reference</label>
                        <input type="text" name="transaction_reference" placeholder="e.g., M-Pesa code, Bank reference">
                    </div>
                    <div class="form-group">
                        <label>Payment Date *</label>
                        <input type="datetime-local" name="payment_date" value="<?php echo date('Y-m-d\TH:i'); ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label>Payment Notes</label>
                    <textarea name="notes" rows="3" placeholder="Add any notes about this payment..."></textarea>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button type="submit" name="record_payment" class="btn">Record Payment</button>
                    <a href="payments.php" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                </div>
            </form>
        </div>

        <?php if(empty($customer_policies)): ?>
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 20px;">
                <div style="text-align: center; color: #6c757d;">
                    <h3>No Active Policies Found</h3>
                    <p>There are no active customer policies available for payment recording.</p>
                    <p>Please ensure customers have active policies before recording payments.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function showPolicyInfo(select) {
            const option = select.options[select.selectedIndex];
            const policyInfo = document.getElementById('policyInfo');
            
            if(option.value) {
                document.getElementById('customerName').textContent = option.dataset.customer;
                document.getElementById('customerEmail').textContent = option.dataset.email;
                document.getElementById('policyName').textContent = option.dataset.policy;
                document.getElementById('policyType').textContent = option.dataset.type;
                document.getElementById('premiumAmount').textContent = 'KSH ' + parseFloat(option.dataset.premium).toLocaleString();
                
                // Auto-fill amount with premium amount
                document.querySelector('input[name="amount"]').value = option.dataset.premium;
                
                policyInfo.style.display = 'block';
            } else {
                policyInfo.style.display = 'none';
                document.querySelector('input[name="amount"]').value = '';
            }
        }

        // Validate payment amount
        document.querySelector('input[name="amount"]').addEventListener('input', function() {
            if(this.value < 0) {
                this.value = 0;
            }
        });

        // Auto-generate transaction reference for M-Pesa
        document.querySelector('select[name="payment_method"]').addEventListener('change', function() {
            const refInput = document.querySelector('input[name="transaction_reference"]');
            if(this.value === 'Mpesa') {
                refInput.placeholder = 'e.g., QH7XMKJP9X (M-Pesa transaction code)';
            } else if(this.value === 'Bank Transfer') {
                refInput.placeholder = 'e.g., Bank reference number';
            } else if(this.value === 'Credit Card' || this.value === 'Debit Card') {
                refInput.placeholder = 'e.g., Last 4 digits of card';
            } else if(this.value === 'Cash') {
                refInput.placeholder = 'e.g., Receipt number';
            } else if(this.value === 'Cheque') {
                refInput.placeholder = 'e.g., Cheque number';
            } else {
                refInput.placeholder = 'Transaction reference';
            }
        });
    </script>
</body>
</html>
