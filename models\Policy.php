<?php
class Policy {
    private $conn;
    private $table_name = "policies";

    // Policy properties
    public $policy_id;
    public $policy_name;
    public $policy_type_id;
    public $description;
    public $duration;
    public $premium_amount;
    public $coverage_amount;
    public $terms_conditions;
    public $created_by;
    public $created_at;
    public $updated_at;

    // Constructor
    public function __construct($db) {
        $this->conn = $db;
    }

    // Read all policies
    public function readAll() {
        // Query to read all policies
        $query = "SELECT p.*, pt.type_name, CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                  FROM " . $this->table_name . " p
                  JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                  JOIN users u ON p.created_by = u.user_id
                  ORDER BY p.policy_name";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    // Read single policy
    public function readOne() {
        // Query to read single policy
        $query = "SELECT p.*, pt.type_name, CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                  FROM " . $this->table_name . " p
                  JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                  JOIN users u ON p.created_by = u.user_id
                  WHERE p.policy_id = :policy_id
                  LIMIT 0,1";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bindParam(":policy_id", $this->policy_id);

        // Execute query
        $stmt->execute();

        // Fetch row
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            // Set properties
            $this->policy_name = $row['policy_name'];
            $this->policy_type_id = $row['policy_type_id'];
            $this->description = $row['description'];
            $this->duration = $row['duration'];
            $this->premium_amount = $row['premium_amount'];
            $this->coverage_amount = $row['coverage_amount'];
            $this->terms_conditions = $row['terms_conditions'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    // Create new policy
    public function create() {
        // Sanitize inputs
        $this->policy_name = htmlspecialchars(strip_tags($this->policy_name));
        $this->policy_type_id = htmlspecialchars(strip_tags($this->policy_type_id));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->duration = htmlspecialchars(strip_tags($this->duration));
        $this->premium_amount = htmlspecialchars(strip_tags($this->premium_amount));
        $this->coverage_amount = htmlspecialchars(strip_tags($this->coverage_amount));
        $this->terms_conditions = htmlspecialchars(strip_tags($this->terms_conditions));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));

        // Insert query
        $query = "INSERT INTO " . $this->table_name . "
                SET policy_name = :policy_name,
                    policy_type_id = :policy_type_id,
                    description = :description,
                    duration = :duration,
                    premium_amount = :premium_amount,
                    coverage_amount = :coverage_amount,
                    terms_conditions = :terms_conditions,
                    created_by = :created_by";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind values
        $stmt->bindParam(":policy_name", $this->policy_name);
        $stmt->bindParam(":policy_type_id", $this->policy_type_id);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":duration", $this->duration);
        $stmt->bindParam(":premium_amount", $this->premium_amount);
        $stmt->bindParam(":coverage_amount", $this->coverage_amount);
        $stmt->bindParam(":terms_conditions", $this->terms_conditions);
        $stmt->bindParam(":created_by", $this->created_by);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Update policy
    public function update() {
        // Sanitize inputs
        $this->policy_id = htmlspecialchars(strip_tags($this->policy_id));
        $this->policy_name = htmlspecialchars(strip_tags($this->policy_name));
        $this->policy_type_id = htmlspecialchars(strip_tags($this->policy_type_id));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->duration = htmlspecialchars(strip_tags($this->duration));
        $this->premium_amount = htmlspecialchars(strip_tags($this->premium_amount));
        $this->coverage_amount = htmlspecialchars(strip_tags($this->coverage_amount));
        $this->terms_conditions = htmlspecialchars(strip_tags($this->terms_conditions));

        // Update query
        $query = "UPDATE " . $this->table_name . "
                SET policy_name = :policy_name,
                    policy_type_id = :policy_type_id,
                    description = :description,
                    duration = :duration,
                    premium_amount = :premium_amount,
                    coverage_amount = :coverage_amount,
                    terms_conditions = :terms_conditions
                WHERE policy_id = :policy_id";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind values
        $stmt->bindParam(":policy_id", $this->policy_id);
        $stmt->bindParam(":policy_name", $this->policy_name);
        $stmt->bindParam(":policy_type_id", $this->policy_type_id);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":duration", $this->duration);
        $stmt->bindParam(":premium_amount", $this->premium_amount);
        $stmt->bindParam(":coverage_amount", $this->coverage_amount);
        $stmt->bindParam(":terms_conditions", $this->terms_conditions);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Delete policy
    public function delete() {
        // Sanitize ID
        $this->policy_id = htmlspecialchars(strip_tags($this->policy_id));

        // Delete query
        $query = "DELETE FROM " . $this->table_name . " WHERE policy_id = :policy_id";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bindParam(":policy_id", $this->policy_id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Get policies by type
    public function readByType($type_id) {
        // Query to get policies by type
        $query = "SELECT p.*, pt.type_name
                  FROM " . $this->table_name . " p
                  JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                  WHERE p.policy_type_id = :type_id
                  ORDER BY p.policy_name";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bindParam(":type_id", $type_id);

        // Execute query
        $stmt->execute();

        return $stmt;
    }
}
?>