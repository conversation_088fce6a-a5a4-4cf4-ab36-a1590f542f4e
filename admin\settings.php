<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle form submissions
if($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['add_policy_type'])) {
        $type_name = trim($_POST['type_name']);
        $description = trim($_POST['description']);
        
        if(!empty($type_name)) {
            try {
                $stmt = $conn->prepare("INSERT INTO policy_types (type_name, description) VALUES (:type_name, :description)");
                $stmt->bindParam(':type_name', $type_name);
                $stmt->bindParam(':description', $description);
                
                if($stmt->execute()) {
                    $success_message = "Policy type added successfully!";
                } else {
                    $error_message = "Error adding policy type.";
                }
            } catch(PDOException $e) {
                $error_message = "Error: " . $e->getMessage();
            }
        } else {
            $error_message = "Policy type name is required.";
        }
    }
    
    if(isset($_POST['add_role'])) {
        $role_name = trim($_POST['role_name']);
        $role_description = trim($_POST['role_description']);
        
        if(!empty($role_name)) {
            try {
                $stmt = $conn->prepare("INSERT INTO roles (role_name, description) VALUES (:role_name, :description)");
                $stmt->bindParam(':role_name', $role_name);
                $stmt->bindParam(':description', $role_description);
                
                if($stmt->execute()) {
                    $success_message = "Role added successfully!";
                } else {
                    $error_message = "Error adding role.";
                }
            } catch(PDOException $e) {
                $error_message = "Error: " . $e->getMessage();
            }
        } else {
            $error_message = "Role name is required.";
        }
    }
}

// Get existing policy types
$policy_types_query = "SELECT * FROM policy_types ORDER BY type_name";
$policy_types_stmt = $conn->prepare($policy_types_query);
$policy_types_stmt->execute();
$policy_types = $policy_types_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get existing roles
$roles_query = "SELECT * FROM roles ORDER BY role_name";
$roles_stmt = $conn->prepare($roles_query);
$roles_stmt->execute();
$roles = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "System Settings";
$page_description = "Configure system parameters and settings.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #6c757d; color: white; padding: 20px 0; margin-bottom: 30px; }
        .settings-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .setting-card { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .setting-card h3 { color: #0056b3; margin-bottom: 15px; border-bottom: 2px solid #0056b3; padding-bottom: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background-color: #0056b3; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background-color: #004494; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table th, .table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #6c757d; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .system-info { background-color: #e9ecef; padding: 15px; border-radius: 6px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <?php if(!empty($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if(!empty($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- System Information -->
        <div class="system-info">
            <h3>System Information</h3>
            <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
            <p><strong>Database:</strong> MySQL</p>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
            <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <div class="settings-grid">
            <!-- Policy Types Management -->
            <div class="setting-card">
                <h3>Policy Types</h3>
                
                <form method="post" style="margin-bottom: 20px;">
                    <div class="form-group">
                        <label>Add New Policy Type</label>
                        <input type="text" name="type_name" placeholder="Policy Type Name" required>
                    </div>
                    <div class="form-group">
                        <textarea name="description" placeholder="Description" rows="2"></textarea>
                    </div>
                    <button type="submit" name="add_policy_type" class="btn">Add Policy Type</button>
                </form>

                <table class="table">
                    <thead>
                        <tr>
                            <th>Type Name</th>
                            <th>Description</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($policy_types as $type): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($type['type_name']); ?></td>
                            <td><?php echo htmlspecialchars($type['description']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($type['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Roles Management -->
            <div class="setting-card">
                <h3>User Roles</h3>
                
                <form method="post" style="margin-bottom: 20px;">
                    <div class="form-group">
                        <label>Add New Role</label>
                        <input type="text" name="role_name" placeholder="Role Name" required>
                    </div>
                    <div class="form-group">
                        <textarea name="role_description" placeholder="Role Description" rows="2"></textarea>
                    </div>
                    <button type="submit" name="add_role" class="btn">Add Role</button>
                </form>

                <table class="table">
                    <thead>
                        <tr>
                            <th>Role Name</th>
                            <th>Description</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($roles as $role): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($role['role_name']); ?></td>
                            <td><?php echo htmlspecialchars($role['description']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($role['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Database Maintenance -->
            <div class="setting-card">
                <h3>Database Maintenance</h3>
                
                <div style="margin-bottom: 15px;">
                    <p><strong>Database Size:</strong> 
                    <?php
                    try {
                        $size_query = "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' 
                                      FROM information_schema.tables 
                                      WHERE table_schema = 'zamara_db'";
                        $size_stmt = $conn->prepare($size_query);
                        $size_stmt->execute();
                        $size = $size_stmt->fetch(PDO::FETCH_ASSOC);
                        echo $size['DB Size in MB'] . ' MB';
                    } catch(PDOException $e) {
                        echo 'Unable to calculate';
                    }
                    ?>
                    </p>
                </div>

                <div style="margin-bottom: 15px;">
                    <p><strong>Table Status:</strong></p>
                    <?php
                    try {
                        $tables_query = "SHOW TABLES";
                        $tables_stmt = $conn->prepare($tables_query);
                        $tables_stmt->execute();
                        $tables = $tables_stmt->fetchAll(PDO::FETCH_COLUMN);
                        echo '<ul>';
                        foreach($tables as $table) {
                            echo '<li>' . htmlspecialchars($table) . ' ✓</li>';
                        }
                        echo '</ul>';
                    } catch(PDOException $e) {
                        echo 'Unable to check tables';
                    }
                    ?>
                </div>

                <button class="btn" onclick="alert('Database backup feature would be implemented here')">Backup Database</button>
                <button class="btn btn-danger" onclick="if(confirm('Are you sure you want to clear logs?')) alert('Clear logs feature would be implemented here')">Clear Logs</button>
            </div>

            <!-- System Configuration -->
            <div class="setting-card">
                <h3>System Configuration</h3>
                
                <div class="form-group">
                    <label>System Name</label>
                    <input type="text" value="Zamara Insurance Management System" readonly>
                </div>
                
                <div class="form-group">
                    <label>Version</label>
                    <input type="text" value="1.0.0" readonly>
                </div>
                
                <div class="form-group">
                    <label>Timezone</label>
                    <select>
                        <option>Africa/Nairobi</option>
                        <option>UTC</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Date Format</label>
                    <select>
                        <option>Y-m-d</option>
                        <option>d/m/Y</option>
                        <option>m/d/Y</option>
                    </select>
                </div>
                
                <button class="btn" onclick="alert('Configuration save feature would be implemented here')">Save Configuration</button>
            </div>
        </div>
    </div>
</body>
</html>
