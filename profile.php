<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "My Profile";
$page_description = "View and update your profile information.";

// Include database connection
require_once 'includes/db_config.php';

// Include user model
require_once 'models/User.php';

// Initialize variables
$first_name = $last_name = $email = $phone = $address = $city = $country = $date_of_birth = "";
$first_name_err = $last_name_err = $email_err = $phone_err = "";
$success_message = "";
$error_message = "";

// Create user object
$user = new User($conn);
$user->user_id = $_SESSION["user_id"];

// Get user data
if($user->readOne()) {
    $first_name = $user->first_name;
    $last_name = $user->last_name;
    $email = $user->email;
    $phone = $user->phone;
    $address = $user->address;
    $city = $user->city;
    $country = $user->country;
    $date_of_birth = $user->date_of_birth;
}

// Process form data when form is submitted
if($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Check which form was submitted
    if(isset($_POST["update_profile"])) {
        // Validate first name
        if(empty(trim($_POST["first_name"]))) {
            $first_name_err = "Please enter your first name.";
        } else {
            $first_name = trim($_POST["first_name"]);
        }
        
        // Validate last name
        if(empty(trim($_POST["last_name"]))) {
            $last_name_err = "Please enter your last name.";
        } else {
            $last_name = trim($_POST["last_name"]);
        }
        
        // Validate phone (optional)
        if(!empty(trim($_POST["phone"]))) {
            $phone = trim($_POST["phone"]);
        }
        
        // Get other optional fields
        $address = trim($_POST["address"]);
        $city = trim($_POST["city"]);
        $country = trim($_POST["country"]);
        $date_of_birth = trim($_POST["date_of_birth"]);
        
        // Check input errors before updating in database
        if(empty($first_name_err) && empty($last_name_err) && empty($phone_err)) {
            
            // Set user properties
            $user->first_name = $first_name;
            $user->last_name = $last_name;
            $user->phone = $phone;
            $user->address = $address;
            $user->city = $city;
            $user->country = $country;
            $user->date_of_birth = $date_of_birth;
            
            // Update user profile
            if($user->update()) {
                // Update session variables
                $_SESSION["first_name"] = $first_name;
                $_SESSION["last_name"] = $last_name;
                
                $success_message = "Your profile has been updated successfully.";
            } else {
                $error_message = "Something went wrong. Please try again later.";
            }
        }
    } elseif(isset($_POST["change_password"])) {
        // Process password change
        $current_password = trim($_POST["current_password"]);
        $new_password = trim($_POST["new_password"]);
        $confirm_password = trim($_POST["confirm_password"]);
        
        // Validate current password
        if(empty($current_password)) {
            $error_message = "Please enter your current password.";
        } else {
            // Verify current password
            $query = "SELECT password FROM users WHERE user_id = :user_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(":user_id", $_SESSION["user_id"]);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if(!password_verify($current_password, $row["password"])) {
                $error_message = "Current password is incorrect.";
            }
        }
        
        // Validate new password
        if(empty($new_password)) {
            $error_message = "Please enter a new password.";
        } elseif(strlen($new_password) < 6) {
            $error_message = "New password must have at least 6 characters.";
        }
        
        // Validate confirm password
        if(empty($confirm_password)) {
            $error_message = "Please confirm your new password.";
        } elseif($new_password != $confirm_password) {
            $error_message = "New password and confirm password do not match.";
        }
        
        // Check for errors
        if(empty($error_message)) {
            // Change password
            if($user->changePassword($new_password)) {
                $success_message = "Your password has been changed successfully.";
            } else {
                $error_message = "Something went wrong. Please try again later.";
            }
        }
    }
}

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>My Profile</h1>
        <p>View and update your profile information.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">
            
            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>
                    
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>
                
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>
            
            <!-- Main Content -->
            <div>
                <?php if(!empty($success_message)): ?>
                    <div style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if(!empty($error_message)): ?>
                    <div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Profile Information -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h2 style="margin-bottom: 20px;">Profile Information</h2>
                    
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label for="first_name" style="display: block; margin-bottom: 5px; font-weight: 500;">First Name *</label>
                                <input type="text" id="first_name" name="first_name" value="<?php echo $first_name; ?>" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($first_name_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                                <?php if(!empty($first_name_err)): ?>
                                    <span style="color: #dc3545; font-size: 14px;"><?php echo $first_name_err; ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div>
                                <label for="last_name" style="display: block; margin-bottom: 5px; font-weight: 500;">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" value="<?php echo $last_name; ?>" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($last_name_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                                <?php if(!empty($last_name_err)): ?>
                                    <span style="color: #dc3545; font-size: 14px;"><?php echo $last_name_err; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="email" style="display: block; margin-bottom: 5px; font-weight: 500;">Email</label>
                            <input type="email" id="email" value="<?php echo $email; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" disabled>
                            <small style="color: #6c757d; font-size: 12px;">Email cannot be changed. Contact support if you need to update your email.</small>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="phone" style="display: block; margin-bottom: 5px; font-weight: 500;">Phone Number</label>
                            <input type="tel" id="phone" name="phone" value="<?php echo $phone; ?>" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($phone_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                            <?php if(!empty($phone_err)): ?>
                                <span style="color: #dc3545; font-size: 14px;"><?php echo $phone_err; ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="date_of_birth" style="display: block; margin-bottom: 5px; font-weight: 500;">Date of Birth</label>
                            <input type="date" id="date_of_birth" name="date_of_birth" value="<?php echo $date_of_birth; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="address" style="display: block; margin-bottom: 5px; font-weight: 500;">Address</label>
                            <textarea id="address" name="address" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"><?php echo $address; ?></textarea>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label for="city" style="display: block; margin-bottom: 5px; font-weight: 500;">City</label>
                                <input type="text" id="city" name="city" value="<?php echo $city; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            
                            <div>
                                <label for="country" style="display: block; margin-bottom: 5px; font-weight: 500;">Country</label>
                                <input type="text" id="country" name="country" value="<?php echo $country; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                        
                        <div>
                            <input type="hidden" name="update_profile" value="1">
                            <button type="submit" style="background-color: #0056b3; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500;">Update Profile</button>
                        </div>
                    </form>
                </div>
                
                <!-- Change Password -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h2 style="margin-bottom: 20px;">Change Password</h2>
                    
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <div style="margin-bottom: 20px;">
                            <label for="current_password" style="display: block; margin-bottom: 5px; font-weight: 500;">Current Password *</label>
                            <input type="password" id="current_password" name="current_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="new_password" style="display: block; margin-bottom: 5px; font-weight: 500;">New Password *</label>
                            <input type="password" id="new_password" name="new_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <small style="color: #6c757d; font-size: 12px;">Password must be at least 6 characters long.</small>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="confirm_password" style="display: block; margin-bottom: 5px; font-weight: 500;">Confirm New Password *</label>
                            <input type="password" id="confirm_password" name="confirm_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        
                        <div>
                            <input type="hidden" name="change_password" value="1">
                            <button type="submit" style="background-color: #0056b3; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500;">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
