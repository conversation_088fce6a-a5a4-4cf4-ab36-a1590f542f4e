<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "View Policy";
$page_description = "View details of your insurance policy.";

// Include database connection
require_once 'includes/db_config.php';

// Check if policy_id is provided in URL
if(!isset($_GET["id"]) || empty($_GET["id"])) {
    header("location: my-policies.php");
    exit;
}

$policy_id = $_GET["id"];

// Get policy details
$query = "SELECT cp.*, p.policy_name, p.description, p.premium_amount, p.coverage_amount, p.terms_conditions, 
          pt.type_name, CONCAT(u.first_name, ' ', u.last_name) as agent_name, u.email as agent_email, u.phone as agent_phone
          FROM customer_policies cp
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          LEFT JOIN users u ON cp.agent_id = u.user_id
          WHERE cp.customer_policy_id = :policy_id AND cp.customer_id = :customer_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(":policy_id", $policy_id);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();

if($stmt->rowCount() == 0) {
    // Policy not found or doesn't belong to user
    header("location: my-policies.php");
    exit;
}

$policy = $stmt->fetch(PDO::FETCH_ASSOC);

// Get policy claims
$query = "SELECT c.claim_id, c.claim_number, c.claim_date, c.incident_date, c.claim_amount, c.status
          FROM claims c
          WHERE c.customer_policy_id = :policy_id
          ORDER BY c.claim_date DESC";

$stmt = $conn->prepare($query);
$stmt->bindParam(":policy_id", $policy_id);
$stmt->execute();
$claims = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get policy payments
$query = "SELECT p.payment_id, p.payment_number, p.payment_date, p.amount, p.payment_method, p.status
          FROM payments p
          WHERE p.customer_policy_id = :policy_id
          ORDER BY p.payment_date DESC";

$stmt = $conn->prepare($query);
$stmt->bindParam(":policy_id", $policy_id);
$stmt->execute();
$payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Policy Details</h1>
        <p>View details of your insurance policy.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">
            
            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>
                    
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>
                
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>
            
            <!-- Main Content -->
            <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0;"><?php echo htmlspecialchars($policy['policy_name']); ?></h2>
                    <span style="background-color: 
                        <?php 
                            switch($policy['status']) {
                                case 'Active': echo '#28a745'; break;
                                case 'Expired': echo '#dc3545'; break;
                                case 'Cancelled': echo '#6c757d'; break;
                                case 'Pending': echo '#ffc107'; break;
                                default: echo '#6c757d';
                            }
                        ?>; 
                        color: <?php echo ($policy['status'] == 'Pending') ? '#212529' : 'white'; ?>; 
                        padding: 8px 15px; 
                        border-radius: 4px; 
                        font-size: 16px;">
                        <?php echo htmlspecialchars($policy['status']); ?>
                    </span>
                </div>
                
                <!-- Policy Details -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Policy Information</h3>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <p><strong>Policy Number:</strong> <?php echo htmlspecialchars($policy['policy_number']); ?></p>
                            <p><strong>Policy Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                            <p><strong>Start Date:</strong> <?php echo date('M d, Y', strtotime($policy['start_date'])); ?></p>
                            <p><strong>End Date:</strong> <?php echo date('M d, Y', strtotime($policy['end_date'])); ?></p>
                        </div>
                        <div>
                            <p><strong>Premium:</strong> $<?php echo number_format($policy['total_premium'], 2); ?></p>
                            <p><strong>Coverage Amount:</strong> $<?php echo number_format($policy['coverage_amount'], 2); ?></p>
                            <p><strong>Payment Frequency:</strong> <?php echo htmlspecialchars($policy['payment_frequency']); ?></p>
                            <p><strong>Next Payment:</strong> <?php echo $policy['next_payment_date'] ? date('M d, Y', strtotime($policy['next_payment_date'])) : 'N/A'; ?></p>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h4 style="margin-bottom: 10px;">Description</h4>
                        <p style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;"><?php echo nl2br(htmlspecialchars($policy['description'])); ?></p>
                    </div>
                    
                    <?php if(!empty($policy['terms_conditions'])): ?>
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">Terms & Conditions</h4>
                            <div style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;">
                                <?php echo nl2br(htmlspecialchars($policy['terms_conditions'])); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($policy['agent_id']): ?>
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">Your Insurance Agent</h4>
                            <div style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;">
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($policy['agent_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($policy['agent_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($policy['agent_phone']); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div style="display: flex; gap: 10px; margin-top: 30px;">
                        <a href="my-policies.php" style="background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Back to Policies</a>
                        <a href="file-claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="background-color: #28a745; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">File a Claim</a>
                        <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="background-color: #ffc107; color: #212529; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Make Payment</a>
                    </div>
                </div>
                
                <!-- Policy Claims -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Claims History</h3>
                    
                    <?php if(count($claims) > 0): ?>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Claim #</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Date</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Incident Date</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Amount</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Status</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($claims as $claim): ?>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($claim['claim_number']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($claim['claim_date'])); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($claim['incident_date'])); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">$<?php echo number_format($claim['claim_amount'], 2); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <span style="background-color: 
                                                <?php 
                                                    switch($claim['status']) {
                                                        case 'Submitted': echo '#ffc107'; break;
                                                        case 'Under Review': echo '#17a2b8'; break;
                                                        case 'Approved': echo '#28a745'; break;
                                                        case 'Rejected': echo '#dc3545'; break;
                                                        case 'Paid': echo '#20c997'; break;
                                                        default: echo '#6c757d';
                                                    }
                                                ?>; 
                                                color: <?php echo ($claim['status'] == 'Submitted') ? '#212529' : 'white'; ?>; 
                                                padding: 3px 8px; 
                                                border-radius: 4px; 
                                                font-size: 12px;">
                                                <?php echo htmlspecialchars($claim['status']); ?>
                                            </span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <a href="view-claim.php?id=<?php echo $claim['claim_id']; ?>" style="color: #0056b3;">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>No claims have been filed for this policy.</p>
                        <a href="file-claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="display: inline-block; margin-top: 10px; background-color: #28a745; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">File a Claim</a>
                    <?php endif; ?>
                </div>
                
                <!-- Policy Payments -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Payment History</h3>
                    
                    <?php if(count($payments) > 0): ?>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Payment #</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Date</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Amount</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Method</th>
                                    <th style="text-align: left; padding: 10px; border-bottom: 1px solid #ddd;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($payments as $payment): ?>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_number']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">$<?php echo number_format($payment['amount'], 2); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                        <td style="padding: 10px; border-bottom: 1px solid #ddd;">
                                            <span style="background-color: 
                                                <?php 
                                                    switch($payment['status']) {
                                                        case 'Completed': echo '#28a745'; break;
                                                        case 'Pending': echo '#ffc107'; break;
                                                        case 'Failed': echo '#dc3545'; break;
                                                        case 'Refunded': echo '#17a2b8'; break;
                                                        default: echo '#6c757d';
                                                    }
                                                ?>; 
                                                color: <?php echo ($payment['status'] == 'Pending') ? '#212529' : 'white'; ?>; 
                                                padding: 3px 8px; 
                                                border-radius: 4px; 
                                                font-size: 12px;">
                                                <?php echo htmlspecialchars($payment['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <?php if($policy['status'] == 'Active' && $policy['next_payment_date']): ?>
                            <div style="margin-top: 20px;">
                                <p><strong>Next Payment Due:</strong> <?php echo date('M d, Y', strtotime($policy['next_payment_date'])); ?></p>
                                <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="display: inline-block; margin-top: 10px; background-color: #ffc107; color: #212529; padding: 8px 15px; border-radius: 4px; text-decoration: none;">Make Payment</a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <p>No payment records found for this policy.</p>
                        <?php if($policy['status'] == 'Active'): ?>
                            <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="display: inline-block; margin-top: 10px; background-color: #ffc107; color: #212529; padding: 8px 15px; border-radius: 4px; text-decoration: none;">Make Payment</a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
