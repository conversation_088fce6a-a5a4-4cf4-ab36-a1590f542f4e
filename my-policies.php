<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Include database connection
require_once "includes/db_config.php";

// Get customer policies
$query = "SELECT cp.customer_policy_id, cp.policy_number, p.policy_name, pt.type_name, cp.start_date, cp.end_date, cp.status, cp.total_premium, p.coverage_amount, cp.payment_frequency, cp.next_payment_date
          FROM customer_policies cp
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id
          ORDER BY cp.created_at DESC";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$policies = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Set page specific variables
$page_title = "My Policies";
$page_description = "View and manage your insurance policies.";

// Include header
include_once 'includes/header.php';
?>

<section style="padding: 60px 0;">
    <div class="container">
        <h1 style="margin-bottom: 30px; color: #0056b3;">My Policies</h1>

        <?php if(count($policies) > 0): ?>
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy Number</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy Name</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Type</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Start Date</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">End Date</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Status</th>
                                <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($policies as $policy): ?>
                                <tr>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['policy_number']); ?></td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['policy_name']); ?></td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['type_name']); ?></td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($policy['start_date'])); ?></td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($policy['end_date'])); ?></td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                        <span style="display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px;
                                            <?php
                                            switch($policy['status']) {
                                                case 'Active':
                                                    echo 'background-color: #d4edda; color: #155724;';
                                                    break;
                                                case 'Pending':
                                                    echo 'background-color: #fff3cd; color: #856404;';
                                                    break;
                                                case 'Expired':
                                                    echo 'background-color: #f8d7da; color: #721c24;';
                                                    break;
                                                case 'Cancelled':
                                                    echo 'background-color: #f8d7da; color: #721c24;';
                                                    break;
                                                default:
                                                    echo 'background-color: #e2e3e5; color: #383d41;';
                                            }
                                            ?>
                                        ">
                                            <?php echo htmlspecialchars($policy['status']); ?>
                                        </span>
                                    </td>
                                    <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                        <a href="view-policy.php?id=<?php echo $policy['customer_policy_id']; ?>" style="color: #0056b3; text-decoration: none; margin-right: 10px;">View</a>
                                        <?php if($policy['status'] == 'Active'): ?>
                                            <a href="file-claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="color: #28a745; text-decoration: none; margin-right: 10px;">File Claim</a>
                                            <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="color: #ffc107; text-decoration: none;">Make Payment</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <?php foreach($policies as $policy): ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);">
                        <h3 style="margin-bottom: 15px; color: #0056b3;"><?php echo htmlspecialchars($policy['policy_name']); ?></h3>
                        <p style="margin-bottom: 5px;"><strong>Policy Number:</strong> <?php echo htmlspecialchars($policy['policy_number']); ?></p>
                        <p style="margin-bottom: 5px;"><strong>Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                        <p style="margin-bottom: 5px;"><strong>Status:</strong>
                            <span style="display: inline-block; padding: 2px 6px; border-radius: 4px; font-size: 12px;
                                <?php
                                switch($policy['status']) {
                                    case 'Active':
                                        echo 'background-color: #d4edda; color: #155724;';
                                        break;
                                    case 'Pending':
                                        echo 'background-color: #fff3cd; color: #856404;';
                                        break;
                                    case 'Expired':
                                        echo 'background-color: #f8d7da; color: #721c24;';
                                        break;
                                    case 'Cancelled':
                                        echo 'background-color: #f8d7da; color: #721c24;';
                                        break;
                                    default:
                                        echo 'background-color: #e2e3e5; color: #383d41;';
                                }
                                ?>
                            ">
                                <?php echo htmlspecialchars($policy['status']); ?>
                            </span>
                        </p>
                        <p style="margin-bottom: 5px;"><strong>Period:</strong> <?php echo date('M d, Y', strtotime($policy['start_date'])); ?> to <?php echo date('M d, Y', strtotime($policy['end_date'])); ?></p>
                        <p><strong>Premium:</strong> KSH <?php echo number_format($policy['total_premium'], 2); ?></p>
                        <p><strong>Coverage:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>

                        <div style="margin-top: 20px; display: flex; gap: 10px;">
                            <a href="view-policy.php?id=<?php echo $policy['customer_policy_id']; ?>" style="flex: 1; padding: 8px 0; background-color: #0056b3; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-size: 14px;">View Details</a>
                            <?php if($policy['status'] == 'Active'): ?>
                                <a href="file-claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="flex: 1; padding: 8px 0; background-color: #28a745; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-size: 14px;">File Claim</a>
                            <?php endif; ?>
                        </div>

                        <?php if($policy['status'] == 'Active'): ?>
                            <div style="margin-top: 10px;">
                                <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="display: block; padding: 8px 0; background-color: #ffc107; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-size: 14px;">Make Payment</a>
                            </div>
                            <div style="margin-top: 10px; font-size: 12px; color: #6c757d;">
                                <p><strong>Next Payment:</strong> <?php echo date('M d, Y', strtotime($policy['next_payment_date'])); ?></p>
                                <p><strong>Payment Frequency:</strong> <?php echo htmlspecialchars($policy['payment_frequency']); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; text-align: center;">
                <p style="margin-bottom: 20px; font-size: 18px;">You don't have any policies yet.</p>
                <a href="browse_policies.php" style="display: inline-block; padding: 12px 25px; background-color: #0056b3; color: white; border: none; border-radius: 4px; text-decoration: none; font-size: 16px; font-weight: 500;">Explore Our Policies</a>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>