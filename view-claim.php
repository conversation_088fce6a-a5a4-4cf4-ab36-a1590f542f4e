<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "View Claim";
$page_description = "View details of your insurance claim.";

// Include database connection
require_once 'includes/db_config.php';

// Check if claim_id is provided in URL
if(!isset($_GET["id"]) || empty($_GET["id"])) {
    header("location: my-claims.php");
    exit;
}

$claim_id = $_GET["id"];

// Get claim details
$query = "SELECT c.*, cp.policy_number, p.policy_name, pt.type_name,
          CONCAT(u.first_name, ' ', u.last_name) as reviewer_name
          FROM claims c
          JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          LEFT JOIN users u ON c.processed_by = u.user_id
          WHERE c.claim_id = :claim_id AND cp.customer_id = :customer_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(":claim_id", $claim_id);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();

if($stmt->rowCount() == 0) {
    // Claim not found or doesn't belong to user
    header("location: my-claims.php");
    exit;
}

$claim = $stmt->fetch(PDO::FETCH_ASSOC);

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Claim Details</h1>
        <p>View details of your insurance claim.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">

            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>

                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>

            <!-- Main Content -->
            <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0;">Claim #<?php echo htmlspecialchars($claim['claim_number']); ?></h2>
                    <span style="background-color:
                        <?php
                            switch($claim['status']) {
                                case 'Pending': echo '#ffc107'; break;
                                case 'Under Review': echo '#17a2b8'; break;
                                case 'Approved': echo '#28a745'; break;
                                case 'Rejected': echo '#dc3545'; break;
                                case 'Processed': echo '#20c997'; break;
                                default: echo '#6c757d';
                            }
                        ?>;
                        color: <?php echo ($claim['status'] == 'Pending') ? '#212529' : 'white'; ?>;
                        padding: 8px 15px;
                        border-radius: 4px;
                        font-size: 16px;">
                        <?php echo htmlspecialchars($claim['status']); ?>
                    </span>
                </div>

                <!-- Claim Details -->
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Claim Information</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <p><strong>Claim Number:</strong> <?php echo htmlspecialchars($claim['claim_number']); ?></p>
                            <p><strong>Policy Number:</strong> <?php echo htmlspecialchars($claim['policy_number']); ?></p>
                            <p><strong>Policy Name:</strong> <?php echo htmlspecialchars($claim['policy_name']); ?></p>
                            <p><strong>Policy Type:</strong> <?php echo htmlspecialchars($claim['type_name']); ?></p>
                        </div>
                        <div>
                            <p><strong>Claim Submitted:</strong> <?php echo date('M d, Y', strtotime($claim['created_at'])); ?></p>
                            <p><strong>Incident Date:</strong> <?php echo date('M d, Y', strtotime($claim['incident_date'])); ?></p>
                            <p><strong>Claim Amount:</strong> $<?php echo number_format($claim['claim_amount'], 2); ?></p>
                            <p><strong>Status:</strong> <?php echo htmlspecialchars($claim['status']); ?></p>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="margin-bottom: 10px;">Description</h4>
                        <p style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;"><?php echo nl2br(htmlspecialchars($claim['description'])); ?></p>
                    </div>

                    <?php if(!empty($claim['documents'])): ?>
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">Supporting Documents</h4>
                            <p style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;"><?php echo nl2br(htmlspecialchars($claim['documents'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if($claim['status'] != 'Pending'): ?>
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">Review Information</h4>
                            <div style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;">
                                <?php if($claim['processed_by']): ?>
                                    <p><strong>Processed By:</strong> <?php echo htmlspecialchars($claim['reviewer_name']); ?></p>
                                <?php endif; ?>
                                <?php if($claim['processed_at']): ?>
                                    <p><strong>Processed Date:</strong> <?php echo date('M d, Y', strtotime($claim['processed_at'])); ?></p>
                                <?php endif; ?>
                                <?php if(!empty($claim['notes'])): ?>
                                    <p><strong>Processing Notes:</strong></p>
                                    <p><?php echo nl2br(htmlspecialchars($claim['notes'])); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div style="display: flex; gap: 10px; margin-top: 30px;">
                        <a href="my-claims.php" style="background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Back to Claims</a>
                        <?php if($claim['status'] == 'Pending'): ?>
                            <a href="edit-claim.php?id=<?php echo $claim['claim_id']; ?>" style="background-color: #ffc107; color: #212529; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Edit Claim</a>
                        <?php endif; ?>
                        <a href="contact.php?subject=Claim%20Inquiry:%20<?php echo $claim['claim_number']; ?>" style="background-color: #6c757d; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Contact Support</a>
                    </div>
                </div>

                <!-- Claim Timeline -->
                <div style="background-color: #f0f5ff; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Claim Timeline</h3>

                    <div style="position: relative; padding-left: 30px;">
                        <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 2px; background-color: #0056b3;"></div>

                        <div style="position: relative; margin-bottom: 30px;">
                            <div style="position: absolute; left: -30px; top: 0; width: 16px; height: 16px; border-radius: 50%; background-color: #0056b3; border: 3px solid #f0f5ff;"></div>
                            <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                <h4 style="margin-bottom: 5px; color: #0056b3;">Claim Submitted</h4>
                                <p style="margin-bottom: 5px;"><?php echo date('M d, Y', strtotime($claim['created_at'])); ?></p>
                                <p>Your claim has been submitted successfully.</p>
                            </div>
                        </div>

                        <?php if($claim['status'] != 'Pending'): ?>
                            <div style="position: relative; margin-bottom: 30px;">
                                <div style="position: absolute; left: -30px; top: 0; width: 16px; height: 16px; border-radius: 50%; background-color: #17a2b8; border: 3px solid #f0f5ff;"></div>
                                <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                    <h4 style="margin-bottom: 5px; color: #17a2b8;">Claim Under Review</h4>
                                    <p>Your claim is being reviewed by our claims department.</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if(in_array($claim['status'], ['Approved', 'Rejected', 'Processed'])): ?>
                            <div style="position: relative; margin-bottom: 30px;">
                                <div style="position: absolute; left: -30px; top: 0; width: 16px; height: 16px; border-radius: 50%; background-color: <?php echo ($claim['status'] == 'Rejected') ? '#dc3545' : '#28a745'; ?>; border: 3px solid #f0f5ff;"></div>
                                <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                    <h4 style="margin-bottom: 5px; color: <?php echo ($claim['status'] == 'Rejected') ? '#dc3545' : '#28a745'; ?>;">Claim <?php echo ($claim['status'] == 'Rejected') ? 'Rejected' : 'Approved'; ?></h4>
                                    <p style="margin-bottom: 5px;"><?php echo date('M d, Y', strtotime($claim['processed_at'])); ?></p>
                                    <p>Your claim has been <?php echo strtolower($claim['status']); ?>.</p>
                                    <?php if(!empty($claim['notes'])): ?>
                                        <p style="margin-top: 10px;"><strong>Notes:</strong> <?php echo htmlspecialchars($claim['notes']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($claim['status'] == 'Processed'): ?>
                            <div style="position: relative;">
                                <div style="position: absolute; left: -30px; top: 0; width: 16px; height: 16px; border-radius: 50%; background-color: #20c997; border: 3px solid #f0f5ff;"></div>
                                <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                    <h4 style="margin-bottom: 5px; color: #20c997;">Payment Processed</h4>
                                    <p>Payment for your claim has been processed.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
