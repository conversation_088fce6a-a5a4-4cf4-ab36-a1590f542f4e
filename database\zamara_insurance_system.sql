-- Zamara Insurance Management System Database
-- Drop database if exists and create new one
DROP DATABASE IF EXISTS zamara_db;
CREATE DATABASE zamara_db CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE zamara_db;

-- Drop tables if they exist to avoid conflicts (in correct order due to foreign key constraints)
DROP TABLE IF EXISTS system_logs;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS claims;
DROP TABLE IF EXISTS customer_policies;
DROP TABLE IF EXISTS policies;
DROP TABLE IF EXISTS policy_types;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS roles;

-- Create roles table
CREATE TABLE roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert roles
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Manages the entire system, including users and policies'),
('Insurance Agent', 'Registers customers, creates policies, and processes claims'),
('Customer', 'Can register, purchase policies, file claims, and track payments'),
('System Accountant', 'Manages premium payments and financial records');

-- Create users table
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    status ENUM('Active', 'Inactive', 'Suspended') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
) ENGINE=InnoDB;

-- Create user_roles table (many-to-many relationship)
CREATE TABLE user_roles (
    user_role_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_role (user_id, role_id)
) ENGINE=InnoDB;

-- Create policy_types table
CREATE TABLE policy_types (
    policy_type_id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert policy types
INSERT INTO policy_types (type_name, description) VALUES
('Motor', 'Vehicle insurance policies'),
('Health', 'Medical and health insurance policies'),
('Life', 'Life insurance and endowment policies'),
('Business', 'Commercial and business insurance policies');

-- Create policies table
CREATE TABLE policies (
    policy_id INT AUTO_INCREMENT PRIMARY KEY,
    policy_name VARCHAR(100) NOT NULL,
    policy_type_id INT NOT NULL,
    description TEXT,
    duration VARCHAR(50),
    premium_amount DECIMAL(10,2) NOT NULL,
    coverage_amount DECIMAL(12,2) NOT NULL,
    terms_conditions TEXT,
    status ENUM('Active', 'Inactive', 'Discontinued') DEFAULT 'Active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (policy_type_id) REFERENCES policy_types(policy_type_id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT
) ENGINE=InnoDB;

-- Create customer_policies table
CREATE TABLE customer_policies (
    customer_policy_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    policy_id INT NOT NULL,
    agent_id INT,
    policy_number VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('Active', 'Expired', 'Cancelled', 'Suspended') DEFAULT 'Active',
    total_premium DECIMAL(10,2) NOT NULL,
    payment_frequency ENUM('Monthly', 'Quarterly', 'Semi-Annually', 'Annually') DEFAULT 'Monthly',
    next_payment_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(policy_id) ON DELETE RESTRICT,
    FOREIGN KEY (agent_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Create claims table
CREATE TABLE claims (
    claim_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_policy_id INT NOT NULL,
    claim_number VARCHAR(50) NOT NULL UNIQUE,
    incident_date DATE NOT NULL,
    description TEXT NOT NULL,
    claim_amount DECIMAL(10,2) NOT NULL,
    status ENUM('Pending', 'Under Review', 'Approved', 'Rejected', 'Processed') DEFAULT 'Pending',
    documents VARCHAR(255),
    processed_by INT,
    processed_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_policy_id) REFERENCES customer_policies(customer_policy_id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Create payments table
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_policy_id INT NOT NULL,
    payment_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_method ENUM('Credit Card', 'Debit Card', 'Bank Transfer', 'Mpesa', 'Cash') NOT NULL,
    status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
    transaction_reference VARCHAR(100),
    recorded_by INT,
    verified_by INT,
    verified_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_policy_id) REFERENCES customer_policies(customer_policy_id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Create system_logs table for audit trail
CREATE TABLE system_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, phone, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '+************', 'Active');

-- Insert sample agent user (password: agent123)
INSERT INTO users (username, email, password, first_name, last_name, phone, status) VALUES
('agent1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Agent', '+************', 'Active');

-- Insert sample accountant user (password: accountant123)
INSERT INTO users (username, email, password, first_name, last_name, phone, status) VALUES
('accountant1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Accountant', '+************', 'Active');

-- Insert sample customer user (password: customer123)
INSERT INTO users (username, email, password, first_name, last_name, phone, status) VALUES
('customer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mary', 'Customer', '+************', 'Active');

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES
(1, 1, 1), -- admin as Administrator
(2, 2, 1), -- agent as Insurance Agent
(3, 4, 1), -- accountant as System Accountant
(4, 3, 1); -- customer as Customer

-- Insert sample policies
INSERT INTO policies (policy_id, policy_name, policy_type_id, description, duration, premium_amount, coverage_amount, terms_conditions, created_by) VALUES
(1, 'Comprehensive Motor Insurance', 1, 'Provides coverage for damage to your vehicle as well as third-party liability.', '12 months', 15000.00, 1000000.00, 'This policy covers damage to your vehicle due to accidents, theft, fire, and natural disasters. It also covers third-party liability for bodily injury and property damage.', 1),
(2, 'Basic Health Insurance', 2, 'Covers basic medical expenses including hospitalization, surgery, and prescription drugs.', '12 months', 12000.00, 500000.00, 'This policy covers hospitalization, surgery, and prescription drugs. Pre-existing conditions may have a waiting period of 6 months. Dental and vision care are not covered.', 1),
(3, 'Term Life Insurance', 3, 'Provides financial protection to your beneficiaries in case of your death during the policy term.', '10 years', 8000.00, 2000000.00, 'This policy provides a death benefit to your beneficiaries if you die during the policy term. Suicide is not covered in the first two years of the policy.', 1),
(4, 'Business Property Insurance', 4, 'Protects your business property against damage or loss due to fire, theft, and natural disasters.', '12 months', 25000.00, 5000000.00, 'This policy covers damage to your business property due to fire, theft, and natural disasters. Business interruption coverage is included for up to 3 months.', 1),
(5, 'Third-Party Motor Insurance', 1, 'Basic coverage for third-party liability only, as required by law.', '12 months', 7500.00, 500000.00, 'This policy covers third-party liability for bodily injury and property damage. It does not cover damage to your own vehicle.', 1),
(6, 'Commercial Vehicle Insurance', 1, 'Specialized coverage for commercial vehicles including trucks, vans, and taxis.', '12 months', 20000.00, 1500000.00, 'This policy covers commercial vehicles for damage, theft, and third-party liability. Additional coverage for goods in transit is available.', 1),
(7, 'Premium Health Insurance', 2, 'Comprehensive health coverage including dental, vision, and mental health services.', '12 months', 24000.00, 1000000.00, 'This policy covers all medical expenses including hospitalization, surgery, prescription drugs, dental, vision, and mental health services. Pre-existing conditions have a waiting period of 3 months.', 1),
(8, 'Family Health Plan', 2, 'Comprehensive health coverage for the entire family with special benefits for children.', '12 months', 36000.00, 1500000.00, 'This policy covers all medical expenses for up to 4 family members. Special benefits include pediatric care, maternity coverage, and preventive health checkups.', 1),
(9, 'Whole Life Insurance', 3, 'Lifetime coverage with a cash value component that grows over time.', 'Lifetime', 15000.00, 3000000.00, 'This policy provides lifetime coverage with a cash value component that grows over time. You can borrow against the cash value or surrender the policy for its cash value.', 1),
(10, 'Endowment Policy', 3, 'Combines life insurance with savings, providing a lump sum payment at the end of the term.', '15 years', 12000.00, 1500000.00, 'This policy provides life insurance coverage and a lump sum payment at the end of the term. Premiums are higher than term life insurance but provide a guaranteed return.', 1),
(11, 'Professional Liability Insurance', 4, 'Protects professionals against claims of negligence or failure to perform their professional duties.', '12 months', 18000.00, 3000000.00, 'This policy covers legal costs and damages awarded in claims of professional negligence. It does not cover criminal prosecution or certain liabilities that may be specified in the policy.', 1),
(12, 'Workers Compensation Insurance', 4, 'Provides coverage for employee injuries or illnesses that occur as a result of their job.', '12 months', 30000.00, 4000000.00, 'This policy covers medical expenses, rehabilitation costs, and lost wages for employees who are injured or become ill as a result of their job. It also provides death benefits to dependents of workers who are killed on the job.', 1);

-- Insert sample customer policy for demonstration
INSERT INTO customer_policies (customer_id, policy_id, agent_id, policy_number, start_date, end_date, total_premium, payment_frequency, next_payment_date) VALUES
(4, 1, 2, 'POL-20241201-0001', '2024-01-01', '2024-12-31', 15000.00, 'Monthly', '2024-02-01');

-- Insert sample payment
INSERT INTO payments (customer_policy_id, payment_number, amount, payment_method, status, recorded_by) VALUES
(1, 'PAY-20241201-0001', 1250.00, 'Mpesa', 'Completed', 4);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_customer_policies_customer ON customer_policies(customer_id);
CREATE INDEX idx_customer_policies_agent ON customer_policies(agent_id);
CREATE INDEX idx_customer_policies_status ON customer_policies(status);
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_date ON payments(payment_date);

-- Create views for easier data access
CREATE VIEW user_roles_view AS
SELECT
    u.user_id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    u.status as user_status,
    GROUP_CONCAT(r.role_name) as roles,
    u.created_at,
    u.last_login
FROM users u
LEFT JOIN user_roles ur ON u.user_id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.role_id
GROUP BY u.user_id;

CREATE VIEW active_policies_view AS
SELECT
    cp.customer_policy_id,
    cp.policy_number,
    u.first_name,
    u.last_name,
    u.email,
    p.policy_name,
    pt.type_name,
    cp.start_date,
    cp.end_date,
    cp.status,
    cp.total_premium,
    cp.payment_frequency,
    cp.next_payment_date,
    agent.first_name as agent_first_name,
    agent.last_name as agent_last_name
FROM customer_policies cp
JOIN users u ON cp.customer_id = u.user_id
JOIN policies p ON cp.policy_id = p.policy_id
JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
LEFT JOIN users agent ON cp.agent_id = agent.user_id
WHERE cp.status = 'Active';

-- Success message
SELECT 'Database created successfully! You can now log in with:' as message
UNION ALL
SELECT 'Administrator: username=admin, password=admin123'
UNION ALL
SELECT 'Agent: username=agent1, password=agent123'
UNION ALL
SELECT 'Accountant: username=accountant1, password=accountant123'
UNION ALL
SELECT 'Customer: username=customer1, password=customer123';

-- End of script
