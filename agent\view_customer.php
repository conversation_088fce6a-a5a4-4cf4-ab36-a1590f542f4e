<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$customer_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($customer_id <= 0) {
    header("location: customers.php");
    exit;
}

// Get customer details with their policies
$query = "SELECT u.*, 
                 COUNT(cp.customer_policy_id) as total_policies,
                 SUM(CASE WHEN cp.status = 'Active' THEN 1 ELSE 0 END) as active_policies,
                 SUM(CASE WHEN cp.status = 'Expired' THEN 1 ELSE 0 END) as expired_policies,
                 COUNT(c.claim_id) as total_claims,
                 SUM(p.amount) as total_payments
          FROM users u
          JOIN user_roles ur ON u.user_id = ur.user_id
          JOIN roles r ON ur.role_id = r.role_id
          LEFT JOIN customer_policies cp ON u.user_id = cp.customer_id
          LEFT JOIN claims c ON cp.customer_policy_id = c.customer_policy_id
          LEFT JOIN payments p ON cp.customer_policy_id = p.customer_policy_id AND p.status = 'Completed'
          WHERE u.user_id = :customer_id AND r.role_name = 'Customer'
          GROUP BY u.user_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':customer_id', $customer_id);
$stmt->execute();
$customer = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$customer) {
    header("location: customers.php");
    exit;
}

// Get customer's policies
$policies_query = "SELECT cp.*, p.policy_name, pt.type_name, p.premium_amount, p.coverage_amount
                   FROM customer_policies cp
                   JOIN policies p ON cp.policy_id = p.policy_id
                   JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                   WHERE cp.customer_id = :customer_id
                   ORDER BY cp.created_at DESC";

$policies_stmt = $conn->prepare($policies_query);
$policies_stmt->bindParam(':customer_id', $customer_id);
$policies_stmt->execute();
$customer_policies = $policies_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get customer's recent claims
$claims_query = "SELECT c.*, cp.policy_number, p.policy_name
                 FROM claims c
                 JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
                 JOIN policies p ON cp.policy_id = p.policy_id
                 WHERE cp.customer_id = :customer_id
                 ORDER BY c.created_at DESC
                 LIMIT 5";

$claims_stmt = $conn->prepare($claims_query);
$claims_stmt->bindParam(':customer_id', $customer_id);
$claims_stmt->execute();
$recent_claims = $claims_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get customer's recent payments
$payments_query = "SELECT p.*, cp.policy_number, pol.policy_name
                   FROM payments p
                   JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
                   JOIN policies pol ON cp.policy_id = pol.policy_id
                   WHERE cp.customer_id = :customer_id
                   ORDER BY p.payment_date DESC
                   LIMIT 5";

$payments_stmt = $conn->prepare($payments_query);
$payments_stmt->bindParam(':customer_id', $customer_id);
$payments_stmt->execute();
$recent_payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "View Customer - " . $customer['first_name'] . " " . $customer['last_name'];
$page_description = "Customer profile and policy information.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .customer-header { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: start; margin-bottom: 30px; }
        .customer-info { }
        .customer-actions { display: flex; gap: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #28a745; }
        .stat-number { font-size: 24px; font-weight: bold; color: #28a745; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-expired { background-color: #f8d7da; color: #721c24; }
        .status-suspended { background-color: #fff3cd; color: #856404; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-approved { background-color: #d4edda; color: #155724; }
        .status-rejected { background-color: #f8d7da; color: #721c24; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-item { }
        .info-label { font-weight: 600; color: #6c757d; margin-bottom: 5px; }
        .info-value { font-size: 16px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="customers.php" class="back-link">← Back to Customers</a>
        
        <!-- Customer Profile -->
        <div class="content">
            <div class="customer-header">
                <div class="customer-info">
                    <h2><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></h2>
                    <p style="color: #6c757d; margin-bottom: 20px;">Customer ID: <?php echo $customer['user_id']; ?></p>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Email</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['email']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Phone</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['phone'] ?: 'Not provided'); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Username</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['username']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Status</div>
                            <div class="info-value">
                                <span class="status-badge status-<?php echo strtolower($customer['status']); ?>">
                                    <?php echo $customer['status']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Date of Birth</div>
                            <div class="info-value"><?php echo $customer['date_of_birth'] ? date('M d, Y', strtotime($customer['date_of_birth'])) : 'Not provided'; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Gender</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['gender'] ?: 'Not specified'); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Member Since</div>
                            <div class="info-value"><?php echo date('M d, Y', strtotime($customer['created_at'])); ?></div>
                        </div>
                    </div>
                    
                    <?php if($customer['address']): ?>
                    <div class="info-item">
                        <div class="info-label">Address</div>
                        <div class="info-value"><?php echo htmlspecialchars($customer['address']); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="customer-actions">
                    <a href="customer_policies.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-primary">View Policies</a>
                    <a href="assign_policy.php?customer_id=<?php echo $customer['user_id']; ?>" class="btn btn-success">Assign Policy</a>
                    <a href="edit_customer.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-warning">Edit Customer</a>
                </div>
            </div>

            <!-- Customer Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $customer['total_policies']; ?></div>
                    <div class="stat-label">Total Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $customer['active_policies']; ?></div>
                    <div class="stat-label">Active Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $customer['total_claims']; ?></div>
                    <div class="stat-label">Total Claims</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSH <?php echo number_format($customer['total_payments'], 2); ?></div>
                    <div class="stat-label">Total Payments</div>
                </div>
            </div>
        </div>

        <!-- Customer Policies -->
        <div class="content">
            <h3>Customer Policies (<?php echo count($customer_policies); ?>)</h3>
            
            <?php if(!empty($customer_policies)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Policy Number</th>
                            <th>Policy Name</th>
                            <th>Type</th>
                            <th>Premium</th>
                            <th>Coverage</th>
                            <th>Status</th>
                            <th>Start Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($customer_policies as $policy): ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($policy['policy_number']); ?></strong></td>
                            <td><?php echo htmlspecialchars($policy['policy_name']); ?></td>
                            <td><?php echo htmlspecialchars($policy['type_name']); ?></td>
                            <td>KSH <?php echo number_format($policy['premium_amount'], 2); ?></td>
                            <td>KSH <?php echo number_format($policy['coverage_amount'], 2); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                                    <?php echo $policy['status']; ?>
                                </span>
                            </td>
                            <td><?php echo date('M d, Y', strtotime($policy['start_date'])); ?></td>
                            <td>
                                <a href="view_policy.php?id=<?php echo $policy['customer_policy_id']; ?>" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">View</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #6c757d; padding: 20px;">No policies assigned to this customer.</p>
                <div style="text-align: center;">
                    <a href="assign_policy.php?customer_id=<?php echo $customer['user_id']; ?>" class="btn btn-success">Assign First Policy</a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Recent Claims -->
        <?php if(!empty($recent_claims)): ?>
        <div class="content">
            <h3>Recent Claims (<?php echo count($recent_claims); ?>)</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>Claim Number</th>
                        <th>Policy</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($recent_claims as $claim): ?>
                    <tr>
                        <td><strong><?php echo htmlspecialchars($claim['claim_number']); ?></strong></td>
                        <td><?php echo htmlspecialchars($claim['policy_name']); ?></td>
                        <td>KSH <?php echo number_format($claim['claim_amount'], 2); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $claim['status'])); ?>">
                                <?php echo $claim['status']; ?>
                            </span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($claim['created_at'])); ?></td>
                        <td>
                            <a href="view_claim.php?id=<?php echo $claim['claim_id']; ?>" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">View</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Recent Payments -->
        <?php if(!empty($recent_payments)): ?>
        <div class="content">
            <h3>Recent Payments (<?php echo count($recent_payments); ?>)</h3>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>Payment Number</th>
                        <th>Policy</th>
                        <th>Amount</th>
                        <th>Method</th>
                        <th>Status</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($recent_payments as $payment): ?>
                    <tr>
                        <td><strong><?php echo htmlspecialchars($payment['payment_number']); ?></strong></td>
                        <td><?php echo htmlspecialchars($payment['policy_name']); ?></td>
                        <td>KSH <?php echo number_format($payment['amount'], 2); ?></td>
                        <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower($payment['status']); ?>">
                                <?php echo $payment['status']; ?>
                            </span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
