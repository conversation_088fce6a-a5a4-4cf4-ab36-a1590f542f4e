<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$payment_id = isset($_GET['payment_id']) ? (int)$_GET['payment_id'] : 0;

if($payment_id <= 0) {
    header("location: payments.php");
    exit;
}

// Get payment details for receipt
$query = "SELECT p.*, cp.policy_number, pol.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email, customer.phone as customer_phone,
                 customer.address as customer_address
          FROM payments p
          JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
          JOIN policies pol ON cp.policy_id = pol.policy_id
          JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          WHERE p.payment_id = :payment_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':payment_id', $payment_id);
$stmt->execute();
$payment = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$payment) {
    header("location: payments.php");
    exit;
}

$page_title = "Payment Receipt - " . $payment['payment_number'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: white; color: #333; }
        .receipt-container { max-width: 800px; margin: 20px auto; padding: 40px; border: 2px solid #ddd; }
        .company-header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #343a40; padding-bottom: 20px; }
        .company-name { font-size: 28px; font-weight: bold; color: #343a40; margin-bottom: 5px; }
        .company-tagline { font-size: 14px; color: #6c757d; margin-bottom: 10px; }
        .company-details { font-size: 12px; color: #6c757d; }
        .receipt-title { text-align: center; font-size: 24px; font-weight: bold; color: #343a40; margin: 30px 0; }
        .receipt-info { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px; }
        .info-section h4 { color: #343a40; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .info-item { margin-bottom: 8px; display: flex; }
        .info-label { font-weight: 600; min-width: 120px; color: #6c757d; }
        .info-value { flex: 1; }
        .payment-details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0; }
        .amount-section { text-align: center; margin: 30px 0; }
        .amount-label { font-size: 16px; color: #6c757d; margin-bottom: 10px; }
        .amount-value { font-size: 36px; font-weight: bold; color: #28a745; }
        .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #6c757d; border-top: 1px solid #ddd; padding-top: 20px; }
        .print-btn { background-color: #343a40; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 20px auto; display: block; }
        .print-btn:hover { background-color: #23272b; }
        .status-badge { padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; display: inline-block; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .watermark { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); font-size: 100px; color: rgba(0,0,0,0.05); z-index: -1; pointer-events: none; }
        
        @media print {
            body { background-color: white; }
            .print-btn { display: none; }
            .receipt-container { border: none; margin: 0; padding: 20px; }
            .watermark { display: none; }
        }
    </style>
</head>
<body>
    <div class="watermark">ZAMARA</div>
    
    <div class="receipt-container">
        <!-- Company Header -->
        <div class="company-header">
            <div class="company-name">ZAMARA INSURANCE LIMITED</div>
            <div class="company-tagline">Your Trusted Insurance Partner</div>
            <div class="company-details">
                P.O. Box 12345, Nairobi, Kenya<br>
                Tel: +*********** 456 | Email: <EMAIL><br>
                Website: www.zamara.co.ke
            </div>
        </div>

        <!-- Receipt Title -->
        <div class="receipt-title">PAYMENT RECEIPT</div>

        <!-- Receipt Information -->
        <div class="receipt-info">
            <!-- Customer Information -->
            <div class="info-section">
                <h4>Customer Information</h4>
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    <span class="info-value"><?php echo htmlspecialchars($payment['customer_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($payment['customer_email']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phone:</span>
                    <span class="info-value"><?php echo htmlspecialchars($payment['customer_phone'] ?: 'N/A'); ?></span>
                </div>
                <?php if($payment['customer_address']): ?>
                <div class="info-item">
                    <span class="info-label">Address:</span>
                    <span class="info-value"><?php echo htmlspecialchars($payment['customer_address']); ?></span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Receipt Details -->
            <div class="info-section">
                <h4>Receipt Details</h4>
                <div class="info-item">
                    <span class="info-label">Receipt No:</span>
                    <span class="info-value"><?php echo htmlspecialchars($payment['payment_number']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Date:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Time:</span>
                    <span class="info-value"><?php echo date('H:i:s', strtotime($payment['payment_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo strtolower($payment['status']); ?>">
                            <?php echo $payment['status']; ?>
                        </span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="payment-details">
            <h4 style="margin-bottom: 15px; color: #343a40;">Payment Details</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <div class="info-item">
                        <span class="info-label">Policy Number:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['policy_number']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Policy Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['policy_name']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Policy Type:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['type_name']); ?></span>
                    </div>
                </div>
                <div>
                    <div class="info-item">
                        <span class="info-label">Payment Method:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['payment_method']); ?></span>
                    </div>
                    <?php if($payment['transaction_reference']): ?>
                    <div class="info-item">
                        <span class="info-label">Reference:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['transaction_reference']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Amount Section -->
        <div class="amount-section">
            <div class="amount-label">Amount Paid</div>
            <div class="amount-value">KSH <?php echo number_format($payment['amount'], 2); ?></div>
            <div style="margin-top: 10px; font-size: 14px; color: #6c757d;">
                (<?php echo ucwords(convertNumberToWords($payment['amount'])); ?> Shillings Only)
            </div>
        </div>

        <!-- Payment Notes -->
        <?php if($payment['notes']): ?>
        <div style="margin: 30px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
            <h4 style="margin-bottom: 10px; color: #343a40;">Notes</h4>
            <p style="font-size: 14px; line-height: 1.5;"><?php echo nl2br(htmlspecialchars($payment['notes'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Thank you for your payment!</strong></p>
            <p>This is a computer-generated receipt and does not require a signature.</p>
            <p>For any queries, please contact <NAME_EMAIL> or +*********** 456</p>
            <p style="margin-top: 15px;">Generated on: <?php echo date('M d, Y H:i:s'); ?></p>
        </div>

        <!-- Print Button -->
        <button class="print-btn" onclick="window.print()">Print Receipt</button>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>

<?php
// Function to convert number to words
function convertNumberToWords($number) {
    $ones = array(
        0 => 'zero', 1 => 'one', 2 => 'two', 3 => 'three', 4 => 'four', 5 => 'five',
        6 => 'six', 7 => 'seven', 8 => 'eight', 9 => 'nine', 10 => 'ten',
        11 => 'eleven', 12 => 'twelve', 13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
        16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen', 19 => 'nineteen'
    );
    
    $tens = array(
        2 => 'twenty', 3 => 'thirty', 4 => 'forty', 5 => 'fifty',
        6 => 'sixty', 7 => 'seventy', 8 => 'eighty', 9 => 'ninety'
    );
    
    if ($number < 20) {
        return $ones[$number];
    } elseif ($number < 100) {
        return $tens[intval($number / 10)] . ($number % 10 != 0 ? ' ' . $ones[$number % 10] : '');
    } elseif ($number < 1000) {
        return $ones[intval($number / 100)] . ' hundred' . ($number % 100 != 0 ? ' ' . convertNumberToWords($number % 100) : '');
    } elseif ($number < 1000000) {
        return convertNumberToWords(intval($number / 1000)) . ' thousand' . ($number % 1000 != 0 ? ' ' . convertNumberToWords($number % 1000) : '');
    } else {
        return convertNumberToWords(intval($number / 1000000)) . ' million' . ($number % 1000000 != 0 ? ' ' . convertNumberToWords($number % 1000000) : '');
    }
}
?>
