<?php
$page_title = "Import Database";
$page_description = "Import the Zamara database schema.";

include_once 'includes/header.php';

$success_message = "";
$error_message = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {

        require_once 'includes/db_config.php';


        $sql_file = file_get_contents('database/zamara_insurance_system.sql');


        $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, 0);


        $queries = explode(';', $sql_file);


        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $stmt = $conn->prepare($query);
                $stmt->execute();
            }
        }

        $success_message = "Database imported successfully! You can now <a href='login.php'>login</a> with the default admin account:<br>Username: admin<br>Password: admin123";
    } catch (PDOException $e) {
        $error_message = "Error importing database: " . $e->getMessage();
    }
}
?>


<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Import Database</h1>
        <p>Set up the Zamara database schema.</p>
    </div>
</section>


<section id="import-database">
    <div class="container">
        <div style="max-width: 800px; margin: 0 auto; padding: 40px 0;">

            <?php if(!empty($success_message)): ?>
                <div style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if(!empty($error_message)): ?>
                <div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2>Database Setup Instructions</h2>
                <p>This page will import the necessary database schema for the Zamara Insurance Management System. Before proceeding, please ensure that:</p>
                <ol style="margin-left: 20px; margin-top: 10px;">
                    <li>XAMPP is running with MySQL service active.</li>
                    <li>The database configuration in <code>includes/db_config.php</code> is correct.</li>
                </ol>
                <p style="margin-top: 10px;">The import process will:</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>Create the <code>zamara_db</code> database if it doesn't exist.</li>
                    <li>Create all necessary tables for the system.</li>
                    <li>Insert default roles and policy types.</li>
                    <li>Create a default admin user.</li>
                </ul>
            </div>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div style="text-align: center;">
                    <button type="submit" style="background-color: #0056b3; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500; font-size: 16px;">Import Database</button>
                </div>
            </form>

            <div style="margin-top: 30px; text-align: center;">
                <p>After importing the database, you can login with the following accounts:</p>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; text-align: left; display: inline-block;">
                    <h3 style="margin-bottom: 10px; color: #0056b3;">Administrator</h3>
                    <p><strong>Username:</strong> admin</p>
                    <p><strong>Password:</strong> admin123</p>
                </div>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; text-align: left; display: inline-block;">
                    <h3 style="margin-bottom: 10px; color: #28a745;">Insurance Agent</h3>
                    <p><strong>Username:</strong> agent1</p>
                    <p><strong>Password:</strong> agent123</p>
                </div>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; text-align: left; display: inline-block;">
                    <h3 style="margin-bottom: 10px; color: #ffc107;">System Accountant</h3>
                    <p><strong>Username:</strong> accountant1</p>
                    <p><strong>Password:</strong> accountant123</p>
                </div>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; text-align: left; display: inline-block;">
                    <h3 style="margin-bottom: 10px; color: #17a2b8;">Customer</h3>
                    <p><strong>Username:</strong> customer1</p>
                    <p><strong>Password:</strong> customer123</p>
                </div>

                <p style="margin-top: 10px; color: #dc3545;"><strong>Important:</strong> For security reasons, please change the default passwords after your first login.</p>
            </div>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>
