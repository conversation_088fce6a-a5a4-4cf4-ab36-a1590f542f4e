<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$customer_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($customer_id <= 0) {
    header("location: customers.php");
    exit;
}

$success_message = $error_message = "";

// Handle policy status updates
if(isset($_POST['update_policy_status'])) {
    $customer_policy_id = $_POST['customer_policy_id'];
    $new_status = $_POST['status'];
    
    try {
        $stmt = $conn->prepare("UPDATE customer_policies SET status = :status WHERE customer_policy_id = :customer_policy_id");
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':customer_policy_id', $customer_policy_id);
        
        if($stmt->execute()) {
            $success_message = "Policy status updated successfully!";
        } else {
            $error_message = "Error updating policy status.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get customer details
$customer_query = "SELECT u.*, 
                          COUNT(cp.customer_policy_id) as total_policies,
                          SUM(CASE WHEN cp.status = 'Active' THEN 1 ELSE 0 END) as active_policies
                   FROM users u
                   JOIN user_roles ur ON u.user_id = ur.user_id
                   JOIN roles r ON ur.role_id = r.role_id
                   LEFT JOIN customer_policies cp ON u.user_id = cp.customer_id
                   WHERE u.user_id = :customer_id AND r.role_name = 'Customer'
                   GROUP BY u.user_id";

$customer_stmt = $conn->prepare($customer_query);
$customer_stmt->bindParam(':customer_id', $customer_id);
$customer_stmt->execute();
$customer = $customer_stmt->fetch(PDO::FETCH_ASSOC);

if(!$customer) {
    header("location: customers.php");
    exit;
}

// Get customer's policies with detailed information
$policies_query = "SELECT cp.*, p.policy_name, pt.type_name, p.premium_amount, p.coverage_amount, p.duration,
                          COUNT(c.claim_id) as claim_count,
                          SUM(CASE WHEN c.status = 'Approved' THEN c.claim_amount ELSE 0 END) as total_claims_paid,
                          SUM(pay.amount) as total_payments,
                          COUNT(pay.payment_id) as payment_count
                   FROM customer_policies cp
                   JOIN policies p ON cp.policy_id = p.policy_id
                   JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                   LEFT JOIN claims c ON cp.customer_policy_id = c.customer_policy_id
                   LEFT JOIN payments pay ON cp.customer_policy_id = pay.customer_policy_id AND pay.status = 'Completed'
                   WHERE cp.customer_id = :customer_id
                   GROUP BY cp.customer_policy_id
                   ORDER BY cp.created_at DESC";

$policies_stmt = $conn->prepare($policies_query);
$policies_stmt->bindParam(':customer_id', $customer_id);
$policies_stmt->execute();
$customer_policies = $policies_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Policies for " . $customer['first_name'] . " " . $customer['last_name'];
$page_description = "Manage customer policies and coverage details.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #17a2b8; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .customer-header { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: center; margin-bottom: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #17a2b8; }
        .stat-number { font-size: 24px; font-weight: bold; color: #17a2b8; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-expired { background-color: #f8d7da; color: #721c24; }
        .status-suspended { background-color: #fff3cd; color: #856404; }
        .status-cancelled { background-color: #e2e3e5; color: #383d41; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #17a2b8; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .policy-card { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #17a2b8; }
        .policy-header { display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px; }
        .policy-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .detail-item { }
        .detail-label { font-weight: 600; color: #6c757d; font-size: 12px; margin-bottom: 3px; }
        .detail-value { font-size: 14px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 10% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="view_customer.php?id=<?php echo $customer['user_id']; ?>" class="back-link">← Back to Customer Profile</a>
        
        <?php if(!empty($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if(!empty($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Customer Summary -->
        <div class="content">
            <div class="customer-header">
                <div>
                    <h2><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></h2>
                    <p style="color: #6c757d;">Customer ID: <?php echo $customer['user_id']; ?> | Email: <?php echo htmlspecialchars($customer['email']); ?></p>
                </div>
                <div>
                    <a href="assign_policy.php?customer_id=<?php echo $customer['user_id']; ?>" class="btn btn-success">Assign New Policy</a>
                </div>
            </div>

            <!-- Policy Statistics -->
            <div class="stats-grid">
                <?php
                $total_policies = count($customer_policies);
                $active_policies = count(array_filter($customer_policies, function($p) { return $p['status'] == 'Active'; }));
                $total_claims = array_sum(array_column($customer_policies, 'claim_count'));
                $total_payments = array_sum(array_column($customer_policies, 'total_payments'));
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_policies; ?></div>
                    <div class="stat-label">Total Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $active_policies; ?></div>
                    <div class="stat-label">Active Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_claims; ?></div>
                    <div class="stat-label">Total Claims</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSH <?php echo number_format($total_payments, 0); ?></div>
                    <div class="stat-label">Total Payments</div>
                </div>
            </div>
        </div>

        <!-- Policies List -->
        <?php if(!empty($customer_policies)): ?>
            <?php foreach($customer_policies as $policy): ?>
            <div class="policy-card">
                <div class="policy-header">
                    <div>
                        <h3><?php echo htmlspecialchars($policy['policy_name']); ?></h3>
                        <p style="color: #6c757d; margin-bottom: 5px;">
                            Policy #: <?php echo htmlspecialchars($policy['policy_number']); ?> | 
                            Type: <?php echo htmlspecialchars($policy['type_name']); ?>
                        </p>
                        <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                            <?php echo $policy['status']; ?>
                        </span>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="viewPolicyDetails(<?php echo $policy['customer_policy_id']; ?>)" class="btn btn-primary">View Details</button>
                        <button onclick="updatePolicyStatus(<?php echo $policy['customer_policy_id']; ?>, '<?php echo $policy['status']; ?>')" class="btn btn-warning">Update Status</button>
                        <?php if($policy['status'] == 'Active'): ?>
                            <a href="file_claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" class="btn btn-info">File Claim</a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="policy-details">
                    <div class="detail-item">
                        <div class="detail-label">PREMIUM AMOUNT</div>
                        <div class="detail-value"><strong>KSH <?php echo number_format($policy['premium_amount'], 2); ?></strong></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">COVERAGE AMOUNT</div>
                        <div class="detail-value"><strong>KSH <?php echo number_format($policy['coverage_amount'], 2); ?></strong></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">START DATE</div>
                        <div class="detail-value"><?php echo date('M d, Y', strtotime($policy['start_date'])); ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">END DATE</div>
                        <div class="detail-value"><?php echo $policy['end_date'] ? date('M d, Y', strtotime($policy['end_date'])) : 'Ongoing'; ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">CLAIMS FILED</div>
                        <div class="detail-value"><?php echo $policy['claim_count']; ?> claims</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">PAYMENTS MADE</div>
                        <div class="detail-value"><?php echo $policy['payment_count']; ?> payments</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">TOTAL PAID</div>
                        <div class="detail-value">KSH <?php echo number_format($policy['total_payments'], 2); ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">CLAIMS PAID</div>
                        <div class="detail-value">KSH <?php echo number_format($policy['total_claims_paid'], 2); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="content">
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No Policies Found</h3>
                    <p>This customer doesn't have any policies assigned yet.</p>
                    <a href="assign_policy.php?customer_id=<?php echo $customer['user_id']; ?>" class="btn btn-success" style="margin-top: 20px;">Assign First Policy</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Update Status Modal -->
    <div id="updateStatusModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>Update Policy Status</h3>
            <form method="post" id="updateStatusForm">
                <input type="hidden" name="customer_policy_id" id="modalPolicyId">
                
                <div style="margin-bottom: 15px;">
                    <label>New Status:</label>
                    <select name="status" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="Active">Active</option>
                        <option value="Suspended">Suspended</option>
                        <option value="Expired">Expired</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" name="update_policy_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function viewPolicyDetails(policyId) {
            window.open('view_policy_details.php?id=' + policyId, '_blank');
        }

        function updatePolicyStatus(policyId, currentStatus) {
            document.getElementById('modalPolicyId').value = policyId;
            document.querySelector('select[name="status"]').value = currentStatus;
            document.getElementById('updateStatusModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('updateStatusModal').style.display = 'none';
        }

        window.onclick = function(event) {
            const modal = document.getElementById('updateStatusModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
