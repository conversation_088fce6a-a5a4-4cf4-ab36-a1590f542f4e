<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$invoice_id = isset($_GET['invoice_id']) ? (int)$_GET['invoice_id'] : 0;

if($invoice_id <= 0) {
    header("location: invoices.php");
    exit;
}

$success_message = $error_message = "";

// Handle marking invoice as paid
if($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['mark_paid'])) {
    $payment_method = $_POST['payment_method'];
    $transaction_reference = trim($_POST['transaction_reference']);
    $payment_date = $_POST['payment_date'];
    $notes = trim($_POST['notes']);
    
    try {
        $conn->beginTransaction();
        
        // Update invoice status
        $stmt = $conn->prepare("UPDATE invoices SET status = 'Paid', paid_at = :payment_date WHERE invoice_id = :invoice_id");
        $stmt->bindParam(':payment_date', $payment_date);
        $stmt->bindParam(':invoice_id', $invoice_id);
        
        if($stmt->execute()) {
            // Get invoice details for payment record
            $invoice_query = "SELECT i.*, cp.customer_policy_id, i.amount 
                             FROM invoices i 
                             JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id 
                             WHERE i.invoice_id = :invoice_id";
            $invoice_stmt = $conn->prepare($invoice_query);
            $invoice_stmt->bindParam(':invoice_id', $invoice_id);
            $invoice_stmt->execute();
            $invoice = $invoice_stmt->fetch(PDO::FETCH_ASSOC);
            
            if($invoice) {
                // Generate payment number
                $payment_number = 'PAY-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
                
                // Create corresponding payment record
                $payment_stmt = $conn->prepare("INSERT INTO payments (payment_number, customer_policy_id, amount, payment_method, transaction_reference, payment_date, status, notes, recorded_by) VALUES (:payment_number, :customer_policy_id, :amount, :payment_method, :transaction_reference, :payment_date, 'Completed', :notes, :recorded_by)");
                $payment_stmt->bindParam(':payment_number', $payment_number);
                $payment_stmt->bindParam(':customer_policy_id', $invoice['customer_policy_id']);
                $payment_stmt->bindParam(':amount', $invoice['amount']);
                $payment_stmt->bindParam(':payment_method', $payment_method);
                $payment_stmt->bindParam(':transaction_reference', $transaction_reference);
                $payment_stmt->bindParam(':payment_date', $payment_date);
                $payment_stmt->bindParam(':notes', $notes);
                $payment_stmt->bindParam(':recorded_by', $_SESSION['user_id']);
                
                if($payment_stmt->execute()) {
                    $conn->commit();
                    $success_message = "Invoice marked as paid successfully! Payment record created: $payment_number";
                } else {
                    $conn->rollback();
                    $error_message = "Error creating payment record.";
                }
            } else {
                $conn->rollback();
                $error_message = "Invoice not found.";
            }
        } else {
            $conn->rollback();
            $error_message = "Error updating invoice status.";
        }
    } catch(PDOException $e) {
        $conn->rollback();
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get invoice details
$query = "SELECT i.*, cp.policy_number, p.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email
          FROM invoices i
          JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          WHERE i.invoice_id = :invoice_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':invoice_id', $invoice_id);
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$invoice) {
    header("location: invoices.php");
    exit;
}

if($invoice['status'] == 'Paid') {
    header("location: view_invoice.php?id=" . $invoice_id);
    exit;
}

$page_title = "Mark Invoice as Paid - " . $invoice['invoice_number'];
$page_description = "Record payment for invoice.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #6f42c1; color: white; padding: 20px 0; margin-bottom: 30px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .invoice-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #6f42c1; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #6f42c1; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #5a2d91; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #6f42c1; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .info-item { }
        .info-label { font-weight: 600; color: #6c757d; font-size: 12px; margin-bottom: 3px; }
        .info-value { font-size: 14px; }
        .amount-highlight { font-size: 24px; font-weight: bold; color: #6f42c1; text-align: center; padding: 15px; background-color: #f8f9fa; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="view_invoice.php?id=<?php echo $invoice_id; ?>" class="back-link">← Back to Invoice</a>
        
        <div class="form-container">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                    <div style="margin-top: 10px;">
                        <a href="view_invoice.php?id=<?php echo $invoice_id; ?>" class="btn btn-primary">View Invoice</a>
                        <a href="invoices.php" class="btn btn-secondary">Back to Invoices</a>
                    </div>
                </div>
            <?php else: ?>
                <?php if(!empty($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <!-- Invoice Information -->
                <div class="invoice-info">
                    <h3>Invoice Information</h3>
                    <div class="info-grid" style="margin-top: 15px;">
                        <div class="info-item">
                            <div class="info-label">INVOICE NUMBER</div>
                            <div class="info-value"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">CUSTOMER</div>
                            <div class="info-value"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">POLICY</div>
                            <div class="info-value"><?php echo htmlspecialchars($invoice['policy_name']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">DUE DATE</div>
                            <div class="info-value"><?php echo date('M d, Y', strtotime($invoice['due_date'])); ?></div>
                        </div>
                    </div>
                    
                    <div class="amount-highlight">
                        Amount: KSH <?php echo number_format($invoice['amount'], 2); ?>
                    </div>
                </div>

                <!-- Payment Form -->
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . "?invoice_id=" . $invoice_id; ?>" method="post">
                    <h3 style="margin-bottom: 20px; color: #6f42c1;">Payment Details</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Payment Method *</label>
                            <select name="payment_method" required>
                                <option value="">Select Payment Method</option>
                                <option value="Mpesa">M-Pesa</option>
                                <option value="Bank Transfer">Bank Transfer</option>
                                <option value="Credit Card">Credit Card</option>
                                <option value="Debit Card">Debit Card</option>
                                <option value="Cash">Cash</option>
                                <option value="Cheque">Cheque</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Transaction Reference</label>
                            <input type="text" name="transaction_reference" placeholder="e.g., M-Pesa code, Bank reference">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Payment Date *</label>
                        <input type="datetime-local" name="payment_date" value="<?php echo date('Y-m-d\TH:i'); ?>" required>
                    </div>

                    <div class="form-group">
                        <label>Payment Notes</label>
                        <textarea name="notes" rows="3" placeholder="Add any notes about this payment..."></textarea>
                    </div>

                    <div style="display: flex; gap: 10px;">
                        <button type="submit" name="mark_paid" class="btn">Mark as Paid</button>
                        <a href="view_invoice.php?id=<?php echo $invoice_id; ?>" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-generate transaction reference based on payment method
        document.querySelector('select[name="payment_method"]').addEventListener('change', function() {
            const refInput = document.querySelector('input[name="transaction_reference"]');
            if(this.value === 'Mpesa') {
                refInput.placeholder = 'e.g., QH7XMKJP9X (M-Pesa transaction code)';
            } else if(this.value === 'Bank Transfer') {
                refInput.placeholder = 'e.g., Bank reference number';
            } else if(this.value === 'Credit Card' || this.value === 'Debit Card') {
                refInput.placeholder = 'e.g., Last 4 digits of card';
            } else if(this.value === 'Cash') {
                refInput.placeholder = 'e.g., Receipt number';
            } else if(this.value === 'Cheque') {
                refInput.placeholder = 'e.g., Cheque number';
            } else {
                refInput.placeholder = 'Transaction reference';
            }
        });

        // Validate payment date
        document.querySelector('input[name="payment_date"]').addEventListener('change', function() {
            const paymentDate = new Date(this.value);
            const today = new Date();
            
            if(paymentDate > today) {
                if(!confirm('Payment date is in the future. Continue?')) {
                    this.value = '<?php echo date('Y-m-d\TH:i'); ?>';
                }
            }
        });
    </script>
</body>
</html>
