<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$export_type = isset($_GET['type']) ? $_GET['type'] : 'payments';

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $export_type . '_export_' . date('Y-m-d_H-i-s') . '.csv"');

// Open output stream
$output = fopen('php://output', 'w');

try {
    switch($export_type) {
        case 'payments':
            // Export payments data
            $query = "SELECT p.payment_id, p.payment_number, p.amount, p.payment_date, p.payment_method, 
                             p.status, p.transaction_reference, p.notes,
                             cp.policy_number, pol.policy_name, pt.type_name,
                             CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                             customer.email as customer_email,
                             CONCAT(recorder.first_name, ' ', recorder.last_name) as recorded_by_name,
                             CONCAT(verifier.first_name, ' ', verifier.last_name) as verified_by_name,
                             p.created_at
                      FROM payments p
                      JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
                      JOIN policies pol ON cp.policy_id = pol.policy_id
                      JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
                      JOIN users customer ON cp.customer_id = customer.user_id
                      LEFT JOIN users recorder ON p.recorded_by = recorder.user_id
                      LEFT JOIN users verifier ON p.verified_by = verifier.user_id
                      ORDER BY p.payment_date DESC";
            
            // CSV headers
            fputcsv($output, [
                'Payment ID', 'Payment Number', 'Amount', 'Payment Date', 'Payment Method',
                'Status', 'Transaction Reference', 'Customer Name', 'Customer Email',
                'Policy Number', 'Policy Name', 'Policy Type', 'Recorded By', 'Verified By',
                'Notes', 'Created At'
            ]);
            
            $stmt = $conn->prepare($query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['payment_id'],
                    $row['payment_number'],
                    $row['amount'],
                    $row['payment_date'],
                    $row['payment_method'],
                    $row['status'],
                    $row['transaction_reference'] ?: '',
                    $row['customer_name'],
                    $row['customer_email'],
                    $row['policy_number'],
                    $row['policy_name'],
                    $row['type_name'],
                    $row['recorded_by_name'] ?: '',
                    $row['verified_by_name'] ?: '',
                    $row['notes'] ?: '',
                    $row['created_at']
                ]);
            }
            break;

        case 'invoices':
            // Export invoices data
            $query = "SELECT i.invoice_id, i.invoice_number, i.amount, i.due_date, i.status,
                             i.description, i.created_at, i.paid_at,
                             cp.policy_number, p.policy_name, pt.type_name,
                             CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                             customer.email as customer_email,
                             CONCAT(creator.first_name, ' ', creator.last_name) as created_by_name
                      FROM invoices i
                      JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id
                      JOIN policies p ON cp.policy_id = p.policy_id
                      JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                      JOIN users customer ON cp.customer_id = customer.user_id
                      LEFT JOIN users creator ON i.created_by = creator.user_id
                      ORDER BY i.created_at DESC";
            
            // CSV headers
            fputcsv($output, [
                'Invoice ID', 'Invoice Number', 'Amount', 'Due Date', 'Status',
                'Customer Name', 'Customer Email', 'Policy Number', 'Policy Name',
                'Policy Type', 'Description', 'Created By', 'Created At', 'Paid At'
            ]);
            
            $stmt = $conn->prepare($query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['invoice_id'],
                    $row['invoice_number'],
                    $row['amount'],
                    $row['due_date'],
                    $row['status'],
                    $row['customer_name'],
                    $row['customer_email'],
                    $row['policy_number'],
                    $row['policy_name'],
                    $row['type_name'],
                    $row['description'] ?: '',
                    $row['created_by_name'] ?: '',
                    $row['created_at'],
                    $row['paid_at'] ?: ''
                ]);
            }
            break;

        case 'customers':
            // Export customers data
            $query = "SELECT u.user_id, u.username, u.email, u.first_name, u.last_name,
                             u.phone, u.address, u.date_of_birth, u.gender, u.status,
                             u.created_at, u.last_login,
                             COUNT(cp.customer_policy_id) as total_policies,
                             SUM(CASE WHEN cp.status = 'Active' THEN 1 ELSE 0 END) as active_policies,
                             COUNT(c.claim_id) as total_claims,
                             SUM(p.amount) as total_payments
                      FROM users u
                      JOIN user_roles ur ON u.user_id = ur.user_id
                      JOIN roles r ON ur.role_id = r.role_id
                      LEFT JOIN customer_policies cp ON u.user_id = cp.customer_id
                      LEFT JOIN claims c ON cp.customer_policy_id = c.customer_policy_id
                      LEFT JOIN payments p ON cp.customer_policy_id = p.customer_policy_id AND p.status = 'Completed'
                      WHERE r.role_name = 'Customer'
                      GROUP BY u.user_id
                      ORDER BY u.created_at DESC";
            
            // CSV headers
            fputcsv($output, [
                'Customer ID', 'Username', 'Email', 'First Name', 'Last Name',
                'Phone', 'Address', 'Date of Birth', 'Gender', 'Status',
                'Total Policies', 'Active Policies', 'Total Claims', 'Total Payments',
                'Created At', 'Last Login'
            ]);
            
            $stmt = $conn->prepare($query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['user_id'],
                    $row['username'],
                    $row['email'],
                    $row['first_name'],
                    $row['last_name'],
                    $row['phone'] ?: '',
                    $row['address'] ?: '',
                    $row['date_of_birth'] ?: '',
                    $row['gender'] ?: '',
                    $row['status'],
                    $row['total_policies'],
                    $row['active_policies'],
                    $row['total_claims'],
                    $row['total_payments'] ?: '0',
                    $row['created_at'],
                    $row['last_login'] ?: ''
                ]);
            }
            break;

        case 'policies':
            // Export policies data
            $query = "SELECT cp.customer_policy_id, cp.policy_number, cp.start_date, cp.end_date,
                             cp.status, cp.total_premium, cp.payment_frequency,
                             p.policy_name, pt.type_name, p.premium_amount, p.coverage_amount,
                             CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                             customer.email as customer_email,
                             CONCAT(agent.first_name, ' ', agent.last_name) as agent_name,
                             cp.created_at
                      FROM customer_policies cp
                      JOIN policies p ON cp.policy_id = p.policy_id
                      JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                      JOIN users customer ON cp.customer_id = customer.user_id
                      LEFT JOIN users agent ON cp.agent_id = agent.user_id
                      ORDER BY cp.created_at DESC";
            
            // CSV headers
            fputcsv($output, [
                'Policy ID', 'Policy Number', 'Policy Name', 'Policy Type',
                'Customer Name', 'Customer Email', 'Agent Name', 'Start Date',
                'End Date', 'Status', 'Premium Amount', 'Coverage Amount',
                'Total Premium', 'Payment Frequency', 'Created At'
            ]);
            
            $stmt = $conn->prepare($query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['customer_policy_id'],
                    $row['policy_number'],
                    $row['policy_name'],
                    $row['type_name'],
                    $row['customer_name'],
                    $row['customer_email'],
                    $row['agent_name'] ?: '',
                    $row['start_date'],
                    $row['end_date'],
                    $row['status'],
                    $row['premium_amount'],
                    $row['coverage_amount'],
                    $row['total_premium'],
                    $row['payment_frequency'],
                    $row['created_at']
                ]);
            }
            break;

        case 'all':
            // Export summary financial data
            fputcsv($output, ['ZAMARA INSURANCE - COMPLETE FINANCIAL EXPORT']);
            fputcsv($output, ['Generated on: ' . date('Y-m-d H:i:s')]);
            fputcsv($output, ['Generated by: ' . $_SESSION["first_name"] . " " . $_SESSION["last_name"]]);
            fputcsv($output, []);

            // Payment summary
            fputcsv($output, ['PAYMENT SUMMARY']);
            fputcsv($output, ['Payment Method', 'Count', 'Total Amount']);
            
            $summary_query = "SELECT payment_method, COUNT(*) as count, SUM(amount) as total 
                             FROM payments WHERE status = 'Completed' 
                             GROUP BY payment_method ORDER BY total DESC";
            $stmt = $conn->prepare($summary_query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [$row['payment_method'], $row['count'], $row['total']]);
            }
            
            fputcsv($output, []);

            // Monthly revenue
            fputcsv($output, ['MONTHLY REVENUE']);
            fputcsv($output, ['Month', 'Revenue', 'Payment Count']);
            
            $monthly_query = "SELECT DATE_FORMAT(payment_date, '%Y-%m') as month,
                             SUM(amount) as revenue, COUNT(*) as count
                             FROM payments WHERE status = 'Completed'
                             GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                             ORDER BY month DESC LIMIT 12";
            $stmt = $conn->prepare($monthly_query);
            $stmt->execute();
            
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [$row['month'], $row['revenue'], $row['count']]);
            }
            break;

        default:
            fputcsv($output, ['Error: Invalid export type']);
            break;
    }

} catch(PDOException $e) {
    fputcsv($output, ['Error: ' . $e->getMessage()]);
}

// Close output stream
fclose($output);
exit;
?>
