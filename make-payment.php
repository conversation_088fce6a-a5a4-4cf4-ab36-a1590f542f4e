<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "Make Payment";
$page_description = "Make a payment for your insurance policy.";

// Include database connection
require_once 'includes/db_config.php';

// Initialize variables
$policy_id = $amount = $payment_method = "";
$policy_id_err = $amount_err = $payment_method_err = "";
$success_message = "";

// Check if policy_id is provided in URL
if(isset($_GET["policy_id"]) && !empty($_GET["policy_id"])) {
    $policy_id = $_GET["policy_id"];

    // Get policy details
    $query = "SELECT cp.*, p.policy_name, pt.type_name
              FROM customer_policies cp
              JOIN policies p ON cp.policy_id = p.policy_id
              JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
              WHERE cp.customer_policy_id = :policy_id AND cp.customer_id = :customer_id";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(":policy_id", $policy_id);
    $stmt->bindParam(":customer_id", $_SESSION["user_id"]);
    $stmt->execute();

    if($stmt->rowCount() > 0) {
        $policy = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate payment amount based on frequency
        $amount = $policy['total_premium'];
        switch($policy['payment_frequency']) {
            case 'Monthly': $amount = $policy['total_premium'] / 12; break;
            case 'Quarterly': $amount = $policy['total_premium'] / 4; break;
            case 'Semi-Annually': $amount = $policy['total_premium'] / 2; break;
        }
        $amount = number_format($amount, 2, '.', '');
    } else {
        // Policy not found or doesn't belong to user
        header("location: my-policies.php");
        exit;
    }
}

// Process form data when form is submitted
if($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate policy
    if(empty(trim($_POST["policy_id"]))) {
        $policy_id_err = "Policy ID is required.";
    } else {
        $policy_id = trim($_POST["policy_id"]);
    }

    // Validate amount
    if(empty(trim($_POST["amount"]))) {
        $amount_err = "Please enter the payment amount.";
    } elseif(!is_numeric(trim($_POST["amount"])) || trim($_POST["amount"]) <= 0) {
        $amount_err = "Please enter a valid payment amount.";
    } else {
        $amount = trim($_POST["amount"]);
    }

    // Validate payment method
    if(empty(trim($_POST["payment_method"]))) {
        $payment_method_err = "Please select a payment method.";
    } else {
        $payment_method = trim($_POST["payment_method"]);
    }

    // Check input errors before inserting in database
    if(empty($policy_id_err) && empty($amount_err) && empty($payment_method_err)) {

        // Generate payment number
        $payment_number = "PAY-" . date("Ymd") . "-" . rand(1000, 9999);

        // Insert payment
        $query = "INSERT INTO payments (customer_policy_id, payment_number, amount, payment_date, payment_method, status, recorded_by)
                  VALUES (:customer_policy_id, :payment_number, :amount, NOW(), :payment_method, 'Completed', :recorded_by)";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(":customer_policy_id", $policy_id);
        $stmt->bindParam(":payment_number", $payment_number);
        $stmt->bindParam(":amount", $amount);
        $stmt->bindParam(":payment_method", $payment_method);
        $stmt->bindParam(":recorded_by", $_SESSION["user_id"]);

        if($stmt->execute()) {
            // Update next payment date
            $query = "UPDATE customer_policies SET next_payment_date =
                      CASE
                          WHEN payment_frequency = 'Monthly' THEN DATE_ADD(NOW(), INTERVAL 1 MONTH)
                          WHEN payment_frequency = 'Quarterly' THEN DATE_ADD(NOW(), INTERVAL 3 MONTH)
                          WHEN payment_frequency = 'Semi-Annually' THEN DATE_ADD(NOW(), INTERVAL 6 MONTH)
                          ELSE DATE_ADD(NOW(), INTERVAL 1 YEAR)
                      END
                      WHERE customer_policy_id = :policy_id";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(":policy_id", $policy_id);
            $stmt->execute();

            $success_message = "Your payment has been processed successfully. Payment Number: " . $payment_number;
        } else {
            echo "Something went wrong. Please try again later.";
        }
    }
}

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Make a Payment</h1>
        <p>Process a payment for your insurance policy.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">

            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>

                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="#footer" onclick="scrollToFooter(event)" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>

            <!-- Main Content -->
            <div>
                <h2 style="margin-bottom: 20px;">Make a Payment</h2>

                <?php if(!empty($success_message)): ?>
                    <div style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                        <?php echo $success_message; ?>
                        <p style="margin-top: 10px;">
                            <a href="payment-history.php" style="color: #155724; text-decoration: underline;">View Payment History</a> |
                            <a href="my-policies.php" style="color: #155724; text-decoration: underline;">Return to My Policies</a>
                        </p>
                    </div>
                <?php else: ?>
                    <!-- Payment Form -->
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                        <?php if(isset($policy)): ?>
                            <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #ddd;">
                                <h3 style="margin-bottom: 15px; color: #0056b3;">Policy Details</h3>
                                <p><strong>Policy Number:</strong> <?php echo htmlspecialchars($policy['policy_number']); ?></p>
                                <p><strong>Policy Name:</strong> <?php echo htmlspecialchars($policy['policy_name']); ?></p>
                                <p><strong>Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                                <p><strong>Payment Frequency:</strong> <?php echo htmlspecialchars($policy['payment_frequency']); ?></p>
                                <?php if($policy['next_payment_date']): ?>
                                    <p><strong>Next Payment Due:</strong> <?php echo date('M d, Y', strtotime($policy['next_payment_date'])); ?></p>
                                <?php endif; ?>
                            </div>

                            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                <input type="hidden" name="policy_id" value="<?php echo $policy_id; ?>">

                                <div style="margin-bottom: 20px;">
                                    <label for="amount" style="display: block; margin-bottom: 5px; font-weight: 500;">Payment Amount ($) *</label>
                                    <input type="number" id="amount" name="amount" value="<?php echo $amount; ?>" min="0" step="0.01" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($amount_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                                    <?php if(!empty($amount_err)): ?>
                                        <span style="color: #dc3545; font-size: 14px;"><?php echo $amount_err; ?></span>
                                    <?php endif; ?>
                                </div>

                                <div style="margin-bottom: 20px;">
                                    <label for="payment_method" style="display: block; margin-bottom: 5px; font-weight: 500;">Payment Method *</label>
                                    <select id="payment_method" name="payment_method" style="width: 100%; padding: 10px; border: 1px solid <?php echo (!empty($payment_method_err)) ? '#dc3545' : '#ddd'; ?>; border-radius: 4px;">
                                        <option value="">-- Select Payment Method --</option>
                                        <option value="Credit Card" <?php echo ($payment_method == 'Credit Card') ? 'selected' : ''; ?>>Credit Card</option>
                                        <option value="Debit Card" <?php echo ($payment_method == 'Debit Card') ? 'selected' : ''; ?>>Debit Card</option>
                                        <option value="Bank Transfer" <?php echo ($payment_method == 'Bank Transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                                        <option value="Mpesa" <?php echo ($payment_method == 'Mpesa') ? 'selected' : ''; ?>>Mpesa</option>
                                        <option value="Cash" <?php echo ($payment_method == 'Cash') ? 'selected' : ''; ?>>Cash</option>
                                    </select>
                                    <?php if(!empty($payment_method_err)): ?>
                                        <span style="color: #dc3545; font-size: 14px;"><?php echo $payment_method_err; ?></span>
                                    <?php endif; ?>
                                </div>

                                <div style="margin-top: 30px;">
                                    <button type="submit" style="background-color: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500;">Process Payment</button>
                                    <a href="my-policies.php" style="display: inline-block; margin-left: 10px; padding: 12px 30px; background-color: #6c757d; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Cancel</a>
                                </div>
                            </form>
                        <?php else: ?>
                            <p>No policy selected. Please go to <a href="my-policies.php">My Policies</a> and select a policy to make a payment.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Payment Information -->
                    <div style="background-color: #f0f5ff; border-radius: 8px; padding: 20px;">
                        <h3 style="margin-bottom: 15px; color: #0056b3;">Payment Information</h3>
                        <h4 style="margin-bottom: 10px;">Payment Methods</h4>
                        <ul style="padding-left: 20px; margin-bottom: 15px;">
                            <li style="margin-bottom: 5px;"><strong>Credit Card:</strong> Visa, MasterCard, American Express</li>
                            <li style="margin-bottom: 5px;"><strong>Debit Card:</strong> All major debit cards accepted</li>
                            <li style="margin-bottom: 5px;"><strong>Bank Transfer:</strong> Direct deposit to our bank account</li>
                            <li style="margin-bottom: 5px;"><strong>Mpesa:</strong> Mobile money transfer service</li>
                            <li><strong>Cash:</strong> Payment at our office locations</li>
                        </ul>

                        <p style="margin-top: 15px;">For any questions about payments, please contact our finance department.</p>
                        <a href="#footer" onclick="scrollToFooter(event)" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Finance Department</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
