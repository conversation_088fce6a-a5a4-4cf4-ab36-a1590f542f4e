<?php
$page_title = "Contact Us";
$page_description = "Get in touch with Zamara for inquiries about our insurance and financial services.";

$message = '';
$message_class = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $name = htmlspecialchars(strip_tags($_POST['name']));
    $email = htmlspecialchars(strip_tags($_POST['email']));
    $phone = htmlspecialchars(strip_tags($_POST['phone']));
    $subject = htmlspecialchars(strip_tags($_POST['subject']));
    $message_text = htmlspecialchars(strip_tags($_POST['message']));


    if (empty($name) || empty($email) || empty($message_text)) {
        $message = "Please fill in all required fields.";
        $message_class = "error";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "Please enter a valid email address.";
        $message_class = "error";
    } else {

        $message = "Thank you for your message, $name! We will get back to you soon.";
        $message_class = "success";


        $name = $email = $phone = $subject = $message_text = '';
    }
}


include_once 'includes/header.php';
?>


<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>Contact Us</h1>
        <p>We'd love to hear from you. Get in touch with our team.</p>
    </div>
</section>


<section id="contact-info">
    <div class="container">
        <div class="section-title">
            <h2>Get In Touch</h2>
            <p>Have questions about our services? Our team is here to help.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-bottom: 50px;">
            <div style="text-align: center; padding: 30px; background-color: #f8f9fa; border-radius: 8px;">
                <i style="font-size: 36px; color: #0056b3; margin-bottom: 15px;">📞</i>
                <h3>Phone Number</h3>
                <p>+254 123 456 789<br>+254 987 654 321</p>
            </div>

            <div style="text-align: center; padding: 30px; background-color: #f8f9fa; border-radius: 8px;">
                <i style="font-size: 36px; color: #0056b3; margin-bottom: 15px;">✉️</i>
                <h3>Email Address</h3>
                <p><EMAIL><br><EMAIL></p>
            </div>

            <div style="text-align: center; padding: 30px; background-color: #f8f9fa; border-radius: 8px;">
                <i style="font-size: 36px; color: #0056b3; margin-bottom: 15px;">⏰</i>
                <h3>Business Hours</h3>
                <p>Monday - Friday: 8am - 5pm<br>Saturday: 9am - 1pm</p>
            </div>
        </div>

        <div style="max-width: 600px; margin: 0 auto;">

            <div>
                <h2 style="margin-bottom: 20px;">Send Us a Message</h2>

                <?php if (!empty($message)): ?>
                    <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: <?php echo $message_class === 'success' ? '#d4edda' : '#f8d7da'; ?>; color: <?php echo $message_class === 'success' ? '#155724' : '#721c24'; ?>;">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form id="contact-form" method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <div style="margin-bottom: 20px;">
                        <label for="name" style="display: block; margin-bottom: 5px; font-weight: 500;">Name *</label>
                        <input type="text" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="email" style="display: block; margin-bottom: 5px; font-weight: 500;">Email *</label>
                        <input type="email" id="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="phone" style="display: block; margin-bottom: 5px; font-weight: 500;">Phone</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo isset($phone) ? $phone : ''; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="subject" style="display: block; margin-bottom: 5px; font-weight: 500;">Subject</label>
                        <input type="text" id="subject" name="subject" value="<?php echo isset($subject) ? $subject : ''; ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="message" style="display: block; margin-bottom: 5px; font-weight: 500;">Message *</label>
                        <textarea id="message" name="message" rows="5" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required><?php echo isset($message_text) ? $message_text : ''; ?></textarea>
                    </div>

                    <button type="submit" style="background-color: #0056b3; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-weight: 500;">Send Message</button>
                </form>
            </div>
        </div>
    </div>
</section>


<section id="faq" style="background-color: #f0f5ff;">
    <div class="container">
        <div class="section-title">
            <h2>Frequently Asked Questions</h2>
            <p>Find answers to common questions about our services.</p>
        </div>

        <div style="max-width: 800px; margin: 0 auto;">
            <div style="margin-bottom: 20px; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #0056b3; margin-bottom: 10px;">How do I get a quote for insurance?</h3>
                <p>You can get a quote by filling out our contact form, calling our office, or visiting us in person. One of our representatives will get in touch with you to discuss your needs and provide a personalized quote.</p>
            </div>

            <div style="margin-bottom: 20px; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #0056b3; margin-bottom: 10px;">How long does it take to process a claim?</h3>
                <p>The time to process a claim varies depending on the type of insurance and the complexity of the claim. Generally, we aim to process claims as quickly as possible, with most claims being resolved within 7-14 business days.</p>
            </div>

            <div style="margin-bottom: 20px; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #0056b3; margin-bottom: 10px;">Can I customize my insurance policy?</h3>
                <p>Yes, we offer customizable insurance policies to meet your specific needs. Our team will work with you to understand your requirements and tailor a policy that provides the coverage you need at a price you can afford.</p>
            </div>

            <div style="background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #0056b3; margin-bottom: 10px;">How can I pay my premiums?</h3>
                <p>We offer various payment options including bank transfer, mobile money, credit/debit cards, and cash payments at our office. You can also set up automatic payments for your convenience.</p>
            </div>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>
