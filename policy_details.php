<?php
// Initialize the session
session_start();

// Include database connection
require_once "includes/db_config.php";

// Define our predefined policies
$all_policies = [
    // Motor Insurance Policies
    [
        'policy_id' => 1,
        'policy_name' => 'Comprehensive Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Provides coverage for damage to your vehicle as well as third-party liability.',
        'duration' => '12 months',
        'premium_amount' => 15000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers damage to your vehicle due to accidents, theft, fire, and natural disasters. It also covers third-party liability for bodily injury and property damage.',
        'features' => [
            'Comprehensive coverage for your vehicle',
            'Third-party liability protection',
            'Personal accident cover for the driver',
            'Emergency roadside assistance',
            'No-claims bonus protection'
        ],
        'benefits' => [
            'Peace of mind knowing your vehicle is protected',
            'Financial protection against third-party claims',
            'Quick and hassle-free claims process',
            '24/7 customer support',
            'Flexible payment options'
        ]
    ],
    [
        'policy_id' => 5,
        'policy_name' => 'Third-Party Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Basic coverage for third-party liability only, as required by law.',
        'duration' => '12 months',
        'premium_amount' => 7500,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers third-party liability for bodily injury and property damage. It does not cover damage to your own vehicle.',
        'features' => [
            'Third-party liability protection',
            'Legal minimum coverage',
            'Affordable premiums',
            'Easy renewal process',
            'Coverage for all authorized drivers'
        ],
        'benefits' => [
            'Legal compliance with minimum insurance requirements',
            'Financial protection against third-party claims',
            'Lower premium costs compared to comprehensive coverage',
            'Simple claims process',
            'Peace of mind for basic liability protection'
        ]
    ],
    [
        'policy_id' => 6,
        'policy_name' => 'Commercial Vehicle Insurance',
        'type_name' => 'Motor',
        'description' => 'Specialized coverage for commercial vehicles including trucks, vans, and taxis.',
        'duration' => '12 months',
        'premium_amount' => 20000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers commercial vehicles for damage, theft, and third-party liability. Additional coverage for goods in transit is available.',
        'features' => [
            'Coverage for commercial vehicles of all types',
            'Third-party liability protection',
            'Goods in transit coverage available',
            'Driver and passenger coverage',
            'Fleet discounts available'
        ],
        'benefits' => [
            'Protection for business vehicles and assets',
            'Reduced business interruption after accidents',
            'Coverage tailored to commercial needs',
            'Financial protection against liability claims',
            'Peace of mind for business operations'
        ]
    ],

    // Health Insurance Policies
    [
        'policy_id' => 2,
        'policy_name' => 'Basic Health Insurance',
        'type_name' => 'Health',
        'description' => 'Covers basic medical expenses including hospitalization, surgery, and prescription drugs.',
        'duration' => '12 months',
        'premium_amount' => 12000,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers hospitalization, surgery, and prescription drugs. Pre-existing conditions may have a waiting period of 6 months. Dental and vision care are not covered.',
        'features' => [
            'Hospitalization coverage',
            'Surgical procedure coverage',
            'Prescription drug coverage',
            'Outpatient care',
            'Emergency medical services'
        ],
        'benefits' => [
            'Access to quality healthcare',
            'Reduced out-of-pocket medical expenses',
            'Cashless treatment at network hospitals',
            'Tax benefits on premium payments',
            'Coverage for pre and post hospitalization expenses'
        ]
    ],
    [
        'policy_id' => 7,
        'policy_name' => 'Premium Health Insurance',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage including dental, vision, and mental health services.',
        'duration' => '12 months',
        'premium_amount' => 24000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers all medical expenses including hospitalization, surgery, prescription drugs, dental, vision, and mental health services. Pre-existing conditions have a waiting period of 3 months.',
        'features' => [
            'Comprehensive medical coverage',
            'Dental and vision care included',
            'Mental health services',
            'Alternative therapy coverage',
            'Preventive health checkups'
        ],
        'benefits' => [
            'Complete healthcare protection',
            'Reduced waiting periods for pre-existing conditions',
            'Higher coverage limits for specialized treatments',
            'Access to premium healthcare facilities',
            'Comprehensive wellness program'
        ]
    ],
    [
        'policy_id' => 8,
        'policy_name' => 'Family Health Plan',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage for the entire family with special benefits for children.',
        'duration' => '12 months',
        'premium_amount' => 36000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers all medical expenses for up to 4 family members. Special benefits include pediatric care, maternity coverage, and preventive health checkups.',
        'features' => [
            'Coverage for up to 4 family members',
            'Pediatric care specialization',
            'Maternity coverage',
            'Preventive health checkups',
            'Family floater benefit'
        ],
        'benefits' => [
            'Comprehensive protection for the whole family',
            'Cost-effective compared to individual policies',
            'Specialized care for children and expectant mothers',
            'Preventive care to maintain family health',
            'Simplified administration with one policy'
        ]
    ],

    // Life Insurance Policies
    [
        'policy_id' => 3,
        'policy_name' => 'Term Life Insurance',
        'type_name' => 'Life',
        'description' => 'Provides financial protection to your beneficiaries in case of your death during the policy term.',
        'duration' => '10 years',
        'premium_amount' => 8000,
        'coverage_amount' => 2000000,
        'terms_conditions' => 'This policy provides a death benefit to your beneficiaries if you die during the policy term. Suicide is not covered in the first two years of the policy.',
        'features' => [
            'High coverage at affordable premiums',
            'Fixed premium for the entire term',
            'Death benefit paid to beneficiaries',
            'Option to convert to permanent life insurance',
            'Riders available for additional protection'
        ],
        'benefits' => [
            'Financial security for your loved ones',
            'Peace of mind knowing your family is protected',
            'Tax benefits on premium payments',
            'Simple and straightforward coverage',
            'No maturity benefit, pure protection plan'
        ]
    ],
    [
        'policy_id' => 9,
        'policy_name' => 'Whole Life Insurance',
        'type_name' => 'Life',
        'description' => 'Lifetime coverage with a cash value component that grows over time.',
        'duration' => 'Lifetime',
        'premium_amount' => 15000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy provides lifetime coverage with a cash value component that grows over time. You can borrow against the cash value or surrender the policy for its cash value.',
        'features' => [
            'Lifetime coverage',
            'Cash value accumulation',
            'Fixed premiums',
            'Loan facility against cash value',
            'Dividend potential'
        ],
        'benefits' => [
            'Lifelong financial protection for beneficiaries',
            'Cash value growth as a form of forced savings',
            'Tax-deferred growth of cash value',
            'Access to funds through policy loans',
            'Estate planning benefits'
        ]
    ],
    [
        'policy_id' => 10,
        'policy_name' => 'Endowment Policy',
        'type_name' => 'Life',
        'description' => 'Combines life insurance with savings, providing a lump sum payment at the end of the term.',
        'duration' => '15 years',
        'premium_amount' => 12000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy provides life insurance coverage and a lump sum payment at the end of the term. Premiums are higher than term life insurance but provide a guaranteed return.',
        'features' => [
            'Life insurance protection',
            'Guaranteed maturity benefit',
            'Bonus additions',
            'Loan facility',
            'Tax benefits'
        ],
        'benefits' => [
            'Dual benefit of insurance and savings',
            'Guaranteed returns at maturity',
            'Financial discipline through regular premium payments',
            'Tax benefits on premiums and maturity amount',
            'Useful for specific financial goals like education or retirement'
        ]
    ],

    // Business Insurance Policies
    [
        'policy_id' => 4,
        'policy_name' => 'Business Property Insurance',
        'type_name' => 'Business',
        'description' => 'Protects your business property against damage or loss due to fire, theft, and natural disasters.',
        'duration' => '12 months',
        'premium_amount' => 25000,
        'coverage_amount' => 5000000,
        'terms_conditions' => 'This policy covers damage to your business property due to fire, theft, and natural disasters. Business interruption coverage is included for up to 3 months.',
        'features' => [
            'Coverage for building and contents',
            'Business interruption coverage',
            'Equipment breakdown protection',
            'Theft and vandalism coverage',
            'Natural disaster protection'
        ],
        'benefits' => [
            'Protection for your business assets',
            'Minimized financial impact from property damage',
            'Quick business recovery after a loss',
            'Customizable coverage options',
            'Peace of mind for business owners'
        ]
    ],
    [
        'policy_id' => 11,
        'policy_name' => 'Professional Liability Insurance',
        'type_name' => 'Business',
        'description' => 'Protects professionals against claims of negligence or failure to perform their professional duties.',
        'duration' => '12 months',
        'premium_amount' => 18000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy covers legal costs and damages awarded in claims of professional negligence. It does not cover criminal prosecution or certain liabilities that may be specified in the policy.',
        'features' => [
            'Professional negligence coverage',
            'Legal defense costs',
            'Settlements and judgments',
            'Prior acts coverage available',
            'Tailored for specific professions'
        ],
        'benefits' => [
            'Protection against professional liability claims',
            'Coverage for legal expenses',
            'Peace of mind when providing professional services',
            'Protection of business reputation',
            'Customized coverage for your profession'
        ]
    ],
    [
        'policy_id' => 12,
        'policy_name' => 'Workers\' Compensation Insurance',
        'type_name' => 'Business',
        'description' => 'Provides coverage for employee injuries or illnesses that occur as a result of their job.',
        'duration' => '12 months',
        'premium_amount' => 30000,
        'coverage_amount' => 4000000,
        'terms_conditions' => 'This policy covers medical expenses, rehabilitation costs, and lost wages for employees who are injured or become ill as a result of their job. It also provides death benefits to dependents of workers who are killed on the job.',
        'features' => [
            'Medical expense coverage',
            'Disability benefits',
            'Rehabilitation costs',
            'Death benefits',
            'Legal compliance'
        ],
        'benefits' => [
            'Protection for both employers and employees',
            'Compliance with legal requirements',
            'Reduced risk of lawsuits from injured employees',
            'Coverage for medical costs and lost wages',
            'Support for employees during recovery'
        ]
    ]
];

// Check if policy_id is provided in the URL
if(!isset($_GET["id"]) || empty($_GET["id"])) {
    header("location: browse_policies.php");
    exit;
}

$policy_id = $_GET["id"];

// Find the policy by ID
$policy = null;
foreach ($all_policies as $p) {
    if ($p['policy_id'] == $policy_id) {
        $policy = $p;
        break;
    }
}

if (!$policy) {
    // Policy not found
    header("location: browse_policies.php");
    exit;
}

// Check if the policy exists in the database, if not, insert it
$check_query = "SELECT policy_id FROM policies WHERE policy_id = :policy_id";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bindParam(":policy_id", $policy_id);
$check_stmt->execute();

if($check_stmt->rowCount() == 0) {
    try {
        // Start transaction
        $conn->beginTransaction();

        // Get policy type ID or create it if it doesn't exist
        $type_query = "SELECT policy_type_id FROM policy_types WHERE type_name = :type_name";
        $type_stmt = $conn->prepare($type_query);
        $type_stmt->bindParam(":type_name", $policy['type_name']);
        $type_stmt->execute();

        if($type_stmt->rowCount() > 0) {
            $policy_type_id = $type_stmt->fetch(PDO::FETCH_ASSOC)['policy_type_id'];
        } else {
            // Insert new policy type
            $insert_type_query = "INSERT INTO policy_types (type_name, description) VALUES (:type_name, :description)";
            $insert_type_stmt = $conn->prepare($insert_type_query);
            $insert_type_stmt->bindParam(":type_name", $policy['type_name']);
            $insert_type_stmt->bindValue(":description", "Insurance for " . $policy['type_name']);
            $insert_type_stmt->execute();
            $policy_type_id = $conn->lastInsertId();
        }

        // Check if admin user exists, if not create one
        $admin_check_query = "SELECT user_id FROM users WHERE username = 'admin' LIMIT 1";
        $admin_check_stmt = $conn->prepare($admin_check_query);
        $admin_check_stmt->execute();

        if($admin_check_stmt->rowCount() == 0) {
            // Create admin user
            $admin_insert_query = "INSERT INTO users (username, password, email, first_name, last_name)
                                  VALUES ('admin', '$2y$10$8KzO1f8Yd7aX9HQ6Yg9Zp.xJnFmBTPm/CnGlHMfVfOEYcH2sjnVOO', '<EMAIL>', 'Admin', 'User')";
            $conn->exec($admin_insert_query);
            $admin_id = $conn->lastInsertId();

            // Get admin role ID
            $role_query = "SELECT role_id FROM roles WHERE role_name = 'Administrator' LIMIT 1";
            $role_stmt = $conn->prepare($role_query);
            $role_stmt->execute();

            if($role_stmt->rowCount() > 0) {
                $role_id = $role_stmt->fetch(PDO::FETCH_ASSOC)['role_id'];

                // Assign admin role to admin user
                $role_assign_query = "INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)";
                $role_assign_stmt = $conn->prepare($role_assign_query);
                $role_assign_stmt->bindParam(":user_id", $admin_id);
                $role_assign_stmt->bindParam(":role_id", $role_id);
                $role_assign_stmt->execute();
            }
        } else {
            $admin_id = $admin_check_stmt->fetch(PDO::FETCH_ASSOC)['user_id'];
        }

        // Insert the policy
        $insert_policy_query = "INSERT INTO policies (policy_id, policy_name, policy_type_id, description, duration, premium_amount, coverage_amount, terms_conditions, created_by)
                               VALUES (:policy_id, :policy_name, :policy_type_id, :description, :duration, :premium_amount, :coverage_amount, :terms_conditions, :created_by)";

        $insert_policy_stmt = $conn->prepare($insert_policy_query);
        $insert_policy_stmt->bindParam(":policy_id", $policy_id);
        $insert_policy_stmt->bindParam(":policy_name", $policy['policy_name']);
        $insert_policy_stmt->bindParam(":policy_type_id", $policy_type_id);
        $insert_policy_stmt->bindParam(":description", $policy['description']);
        $insert_policy_stmt->bindParam(":duration", $policy['duration']);
        $insert_policy_stmt->bindParam(":premium_amount", $policy['premium_amount']);
        $insert_policy_stmt->bindParam(":coverage_amount", $policy['coverage_amount']);
        $insert_policy_stmt->bindParam(":terms_conditions", $policy['terms_conditions']);
        $insert_policy_stmt->bindParam(":created_by", $admin_id); // Use admin ID
        $insert_policy_stmt->execute();

        // Commit transaction
        $conn->commit();
    } catch(PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        // Just log the error, don't show to user
        error_log("Error inserting policy: " . $e->getMessage());
    }
}

// Get related policies (same type)
$related_policies = [];
foreach ($all_policies as $p) {
    if ($p['type_name'] == $policy['type_name'] && $p['policy_id'] != $policy_id) {
        $related_policies[] = $p;
    }
}
// Limit to 3 related policies
$related_policies = array_slice($related_policies, 0, 3);

// Set page specific variables
$page_title = $policy['policy_name'];
$page_description = $policy['description'];

// Include header
include_once 'includes/header.php';
?>

<section style="padding: 60px 0;">
    <div class="container">
        <div style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
            <div style="flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0 15px;">
                <h1 style="margin-bottom: 20px; color: #0056b3;"><?php echo htmlspecialchars($policy['policy_name']); ?></h1>
                <p style="margin-bottom: 30px; font-size: 18px; color: #6c757d;"><?php echo htmlspecialchars($policy['description']); ?></p>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                    <h2 style="margin-bottom: 20px; color: #0056b3; font-size: 24px;">Policy Details</h2>
                    <div style="display: flex; flex-wrap: wrap; margin: 0 -10px;">
                        <div style="flex: 0 0 50%; max-width: 50%; padding: 0 10px;">
                            <p style="margin-bottom: 15px;"><strong>Policy Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                            <p style="margin-bottom: 15px;"><strong>Duration:</strong> <?php echo htmlspecialchars($policy['duration']); ?></p>
                            <p style="margin-bottom: 15px;"><strong>Premium Amount:</strong> KSH <?php echo number_format($policy['premium_amount'], 2); ?></p>
                        </div>
                        <div style="flex: 0 0 50%; max-width: 50%; padding: 0 10px;">
                            <p style="margin-bottom: 15px;"><strong>Coverage Amount:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>
                            <p style="margin-bottom: 15px;"><strong>Payment Options:</strong> Monthly, Quarterly, Semi-Annually, Annually</p>
                            <p style="margin-bottom: 15px;"><strong>Eligibility:</strong> All Kenyan residents aged 18-65</p>
                        </div>
                    </div>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                    <h2 style="margin-bottom: 20px; color: #0056b3; font-size: 24px;">Key Features</h2>
                    <ul style="padding-left: 20px; margin-bottom: 0;">
                        <?php foreach($policy['features'] as $feature): ?>
                            <li style="margin-bottom: 10px;"><?php echo htmlspecialchars($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
                    <h2 style="margin-bottom: 20px; color: #0056b3; font-size: 24px;">Benefits</h2>
                    <ul style="padding-left: 20px; margin-bottom: 0;">
                        <?php foreach($policy['benefits'] as $benefit): ?>
                            <li style="margin-bottom: 10px;"><?php echo htmlspecialchars($benefit); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px;">
                    <h2 style="margin-bottom: 20px; color: #0056b3; font-size: 24px;">Terms & Conditions</h2>
                    <p style="margin-bottom: 0;"><?php echo nl2br(htmlspecialchars($policy['terms_conditions'])); ?></p>
                </div>
            </div>

            <div style="flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0 15px;">
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 30px; position: sticky; top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3; font-size: 20px;">Apply for This Policy</h3>
                    <div style="margin-bottom: 20px;">
                        <p style="margin-bottom: 5px; font-weight: 700; font-size: 24px; color: #0056b3;">KSH <?php echo number_format($policy['premium_amount'], 2); ?></p>
                        <p style="margin-bottom: 0; color: #6c757d;">per <?php echo strtolower(explode(' ', $policy['duration'])[1]); ?></p>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <p style="margin-bottom: 5px;"><strong>Coverage Amount:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>
                        <p style="margin-bottom: 5px;"><strong>Duration:</strong> <?php echo htmlspecialchars($policy['duration']); ?></p>
                        <p style="margin-bottom: 0;"><strong>Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                    </div>
                    <a href="apply_policy.php?id=<?php echo $policy_id; ?>" style="display: block; width: 100%; padding: 12px 0; background-color: #0056b3; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-weight: 500; margin-bottom: 10px;">Apply Now</a>
                    <a href="browse_policies.php" style="display: block; width: 100%; padding: 12px 0; background-color: #6c757d; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-weight: 500;">Back to Policies</a>
                </div>

                <?php if(count($related_policies) > 0): ?>
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px;">
                        <h3 style="margin-bottom: 20px; color: #0056b3; font-size: 20px;">Related Policies</h3>
                        <?php foreach($related_policies as $related): ?>
                            <div style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #dee2e6;">
                                <h4 style="margin-bottom: 10px; font-size: 18px;"><a href="policy_details.php?id=<?php echo $related['policy_id']; ?>" style="color: #0056b3; text-decoration: none;"><?php echo htmlspecialchars($related['policy_name']); ?></a></h4>
                                <p style="margin-bottom: 10px; color: #6c757d;"><?php echo htmlspecialchars($related['description']); ?></p>
                                <p style="margin-bottom: 10px;"><strong>Premium:</strong> KSH <?php echo number_format($related['premium_amount'], 2); ?></p>
                                <a href="policy_details.php?id=<?php echo $related['policy_id']; ?>" style="color: #0056b3; text-decoration: none; font-weight: 500;">View Details</a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>