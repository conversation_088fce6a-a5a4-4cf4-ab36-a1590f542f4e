<?php
session_start();

if(isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
    // Redirect based on primary role
    switch($_SESSION["primary_role"]) {
        case "Administrator":
            header("location: admin/dashboard.php");
            break;
        case "Insurance Agent":
            header("location: agent/dashboard.php");
            break;
        case "System Accountant":
            header("location: accountant/dashboard.php");
            break;
        case "Customer":
        default:
            header("location: dashboard.php");
            break;
    }
    exit;
}

require_once "includes/db_config.php";

$username = $password = "";
$username_err = $password_err = $login_err = "";


if($_SERVER["REQUEST_METHOD"] == "POST") {


    if(empty(trim($_POST["username"]))) {
        $username_err = "Please enter username.";
    } else {
        $username = trim($_POST["username"]);
    }


    if(empty(trim($_POST["password"]))) {
        $password_err = "Please enter your password.";
    } else {
        $password = trim($_POST["password"]);
    }


    if(empty($username_err) && empty($password_err)) {
        $start_user_query = microtime(true); // Start timer

        $sql = "SELECT u.user_id, u.username, u.password, u.first_name, u.last_name
                FROM users u
                WHERE u.username = :username";

        if($stmt = $conn->prepare($sql)) {

            $stmt->bindParam(":username", $param_username, PDO::PARAM_STR);


            $param_username = trim($_POST["username"]);


            if($stmt->execute()) {

                if($stmt->rowCount() == 1) {
                    if($row = $stmt->fetch()) {
                        $id = $row["user_id"];
                        $username = $row["username"];
                        $hashed_password = $row["password"];
                        $first_name = $row["first_name"];
                        $last_name = $row["last_name"];

                        if(password_verify($password, $hashed_password)) {

                            session_start();

                            // Get user roles
                            $roles_sql = "SELECT r.role_id, r.role_name
                                         FROM roles r
                                         JOIN user_roles ur ON r.role_id = ur.role_id
                                         WHERE ur.user_id = :user_id";
                            $roles_stmt = $conn->prepare($roles_sql);
                            $roles_stmt->bindParam(":user_id", $id, PDO::PARAM_INT);
                            $roles_stmt->execute();
                            $roles = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);

                            // Set primary role (first role in the list)
                            $primary_role = !empty($roles) ? $roles[0]["role_name"] : "Customer";

                            $_SESSION["loggedin"] = true;
                            $_SESSION["user_id"] = $id;
                            $_SESSION["username"] = $username;
                            $_SESSION["first_name"] = $first_name;
                            $_SESSION["last_name"] = $last_name;
                            $_SESSION["roles"] = $roles;
                            $_SESSION["primary_role"] = $primary_role;


                            $update_sql = "UPDATE users SET last_login = NOW() WHERE user_id = :user_id";
                            $update_stmt = $conn->prepare($update_sql);
                            $update_stmt->bindParam(":user_id", $id, PDO::PARAM_INT);
                            $update_stmt->execute();

                            // Redirect based on primary role
                            switch($_SESSION["primary_role"]) {
                                case "Administrator":
                                    header("location: admin/dashboard.php");
                                    break;
                                case "Insurance Agent":
                                    header("location: agent/dashboard.php");
                                    break;
                                case "System Accountant":
                                    header("location: accountant/dashboard.php");
                                    break;
                                case "Customer":
                                default:
                                    header("location: dashboard.php");
                                    break;
                            }
                        } else {

                            $login_err = "Invalid username or password.";
                        }
                    }
                } else {

                    $login_err = "Invalid username or password.";
                }
            } else {
                $login_err = "Oops! Something went wrong. Please try again later.";
            }


            unset($stmt);
        }
    }


    unset($conn);
}

$page_title = "Login";
$page_description = "Login to your Zamara Insurance account to manage your policies, file claims, and more.";

include_once 'includes/header.php';
?>

<section style="padding: 80px 0;">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto; background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);">
            <h2 style="text-align: center; margin-bottom: 30px; color: #0056b3;">Login to Your Account</h2>

            <?php
            if(!empty($login_err)) {
                echo '<div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;">' . $login_err . '</div>';
            }
            ?>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Username</label>
                    <input type="text" name="username" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $username; ?>">
                    <span style="color: #dc3545; font-size: 14px;"><?php echo $username_err; ?></span>
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Password</label>
                    <input type="password" name="password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                    <span style="color: #dc3545; font-size: 14px;"><?php echo $password_err; ?></span>
                </div>
                <div style="margin-bottom: 20px;">
                    <button type="submit" style="width: 100%; padding: 12px; background-color: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Login</button>
                </div>
                <p style="text-align: center;">Don't have an account? <a href="register.php" style="color: #0056b3; text-decoration: none;">Sign up now</a>.</p>
                <p style="text-align: center;"><a href="forgot-password.php" style="color: #0056b3; text-decoration: none;">Forgot Password?</a></p>
            </form>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>