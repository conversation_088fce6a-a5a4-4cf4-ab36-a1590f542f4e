<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_admin = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Administrator") {
        $is_admin = true;
        break;
    }
}

if(!$is_admin) {
    header("location: ../dashboard.php");
    exit;
}

$page_title = "Admin Dashboard";
$page_description = "Manage the Zamara insurance system.";

require_once '../includes/db_config.php';

require_once '../models/User.php';
require_once '../models/Role.php';
require_once '../models/Policy.php';


$query = "SELECT COUNT(*) as total_users FROM users";
$stmt = $conn->prepare($query);
$stmt->execute();
$user_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];


$query = "SELECT COUNT(*) as total_policies FROM policies";
$stmt = $conn->prepare($query);
$stmt->execute();
$policy_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_policies'];


$query = "SELECT COUNT(*) as total_claims FROM claims";
$stmt = $conn->prepare($query);
$stmt->execute();
$claim_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_claims'];


$query = "SELECT user_id, username, email, first_name, last_name, created_at
          FROM users
          ORDER BY created_at DESC
          LIMIT 5";
$stmt = $conn->prepare($query);
$stmt->execute();
$recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Zamara' : 'Zamara - Insurance & Financial Services'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Zamara offers comprehensive insurance and financial services to individuals and businesses.'; ?>">

    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .admin-header {
            background-color: #343a40;
            color: white;
        }

        .admin-sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 70px);
        }

        .admin-sidebar a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }

        .admin-sidebar a:hover, .admin-sidebar a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .admin-content {
            padding: 20px;
        }

        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin-bottom: 10px;
            color: #343a40;
        }

        .stat-card p {
            font-size: 24px;
            font-weight: 700;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
    </style>
</head>
<body>

    <header class="admin-header">
        <div class="container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
            <div>
                <h2>Zamara Admin</h2>
            </div>

            <div style="display: flex; align-items: center;">
                <span style="margin-right: 15px;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                <a href="../logout.php" style="color: white; text-decoration: none; background-color: #dc3545; padding: 5px 10px; border-radius: 4px;">Logout</a>
            </div>
        </div>
    </header>

    <div style="display: flex;">

        <div class="admin-sidebar" style="width: 250px; padding: 20px;">
            <h3 style="margin-bottom: 20px;">Admin Menu</h3>

            <nav>
                <a href="dashboard.php" class="active">Dashboard</a>
                <a href="manage_users.php">Manage Users</a>
                <a href="roles.php">Manage Roles</a>
                <a href="policies.php">Manage Policies</a>
                <a href="claims.php">Manage Claims</a>
                <a href="payments.php">Manage Payments</a>
                <a href="reports.php">Reports</a>
                <a href="../index.php">View Website</a>
            </nav>
        </div>


        <div class="admin-content" style="flex: 1;">
            <h1 style="margin-bottom: 20px;">Admin Dashboard</h1>


            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="border-top: 4px solid #0056b3;">
                    <h3>Total Users</h3>
                    <p style="color: #0056b3;"><?php echo $user_count; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #28a745;">
                    <h3>Total Policies</h3>
                    <p style="color: #28a745;"><?php echo $policy_count; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #ffc107;">
                    <h3>Total Claims</h3>
                    <p style="color: #ffc107;"><?php echo $claim_count; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #dc3545;">
                    <h3>System Status</h3>
                    <p style="color: #28a745;">Active</p>
                </div>
            </div>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 30px;">
                <h2 style="margin-bottom: 20px;">Recent Users</h2>

                <?php if(count($recent_users) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Registered</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($recent_users as $user): ?>
                                <tr>
                                    <td><?php echo $user['user_id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <a href="manage_users.php?view=<?php echo $user['user_id']; ?>" style="color: #0056b3; margin-right: 10px;">View</a>
                                        <a href="manage_users.php?edit=<?php echo $user['user_id']; ?>" style="color: #28a745; margin-right: 10px;">Edit</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No users found.</p>
                <?php endif; ?>

                <div style="margin-top: 20px;">
                    <a href="manage_users.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Users</a>
                </div>
            </div>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h2 style="margin-bottom: 20px;">Quick Actions</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <a href="add_user.php" style="background-color: #0056b3; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Add New User</h3>
                    </a>

                    <a href="create_policy.php" style="background-color: #28a745; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Create New Policy</h3>
                    </a>

                    <a href="reports.php" style="background-color: #ffc107; color: #212529; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Generate Reports</h3>
                    </a>

                    <a href="settings.php" style="background-color: #6c757d; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>System Settings</h3>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <footer style="background-color: #343a40; color: white; padding: 20px 0; text-align: center; margin-top: 30px;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Zamara Admin Panel. All Rights Reserved.</p>
        </div>
    </footer>


    <script src="../assets/js/main.js"></script>
</body>
</html>
