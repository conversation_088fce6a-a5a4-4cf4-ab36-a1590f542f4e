-- Add invoices table to the database
USE zamara_db;

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
    invoice_id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    customer_policy_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    description TEXT,
    status ENUM('Pending', 'Paid', 'Overdue', 'Cancelled') DEFAULT 'Pending',
    created_by INT,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_policy_id) REFERENCES customer_policies(customer_policy_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Add indexes for better performance
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_due_date ON invoices(due_date);
CREATE INDEX idx_invoices_customer_policy ON invoices(customer_policy_id);

-- Insert sample invoices
INSERT INTO invoices (invoice_number, customer_policy_id, amount, due_date, description, status, created_by) VALUES
('INV-2024-0001', 1, 1250.00, '2024-12-31', 'Monthly premium payment for Motor Insurance', 'Pending', 4),
('INV-2024-0002', 1, 1250.00, '2024-11-30', 'Monthly premium payment for Motor Insurance', 'Paid', 4);

-- Update the paid invoice with payment date
UPDATE invoices SET paid_at = '2024-11-30 10:30:00' WHERE invoice_number = 'INV-2024-0002';

-- Add missing columns to existing tables if they don't exist
ALTER TABLE customer_policies 
ADD COLUMN IF NOT EXISTS assigned_by INT,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD FOREIGN KEY IF NOT EXISTS (assigned_by) REFERENCES users(user_id) ON DELETE SET NULL;

-- Update payment method enum to include more options
ALTER TABLE payments 
MODIFY COLUMN payment_method ENUM('Credit Card', 'Debit Card', 'Bank Transfer', 'Mpesa', 'Cash', 'Cheque') NOT NULL;

-- Add missing columns to payments table if they don't exist
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS transaction_reference VARCHAR(100),
ADD COLUMN IF NOT EXISTS verified_by INT,
ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP NULL,
ADD FOREIGN KEY IF NOT EXISTS (verified_by) REFERENCES users(user_id) ON DELETE SET NULL;

-- Update claims table to match the expected structure
ALTER TABLE claims 
MODIFY COLUMN status ENUM('Pending', 'Under Review', 'Approved', 'Rejected', 'Processed') DEFAULT 'Pending',
ADD COLUMN IF NOT EXISTS processed_by INT,
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD FOREIGN KEY IF NOT EXISTS (processed_by) REFERENCES users(user_id) ON DELETE SET NULL;

-- Rename claim_date to incident_date if it exists
-- ALTER TABLE claims CHANGE COLUMN claim_date incident_date DATE NOT NULL;

COMMIT;
