    </main>
    <footer id="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section about">
                    <h3>About Zamara Insurance</h3>
                    <p>Zamara Insurance is a leading provider of insurance solutions in Kenya, offering a wide range of products to protect what matters most to you.</p>
                    <div class="contact">
                        <span><i class="fas fa-phone"></i> +254 123 456 789</span>
                        <span><i class="fas fa-envelope"></i> <EMAIL></span>
                    </div>
                    <div class="socials">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="browse_policies.php">Policies</a></li>
                        <li><a href="services.php">Services</a></li>
                        <li><a href="#footer" onclick="scrollToFooter(event)">Contact Us</a></li>
                        <li><a href="terms.php">Terms & Conditions</a></li>
                        <li><a href="privacy.php">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section contact-form">
                    <h3>Contact Us</h3>
                    <form id="contactForm" onsubmit="return submitContactForm(event)">
                        <input type="text" name="name" class="text-input contact-input" placeholder="Your name..." required>
                        <input type="email" name="email" class="text-input contact-input" placeholder="Your email address..." required>
                        <textarea name="message" class="text-input contact-input" placeholder="Your message..." required></textarea>
                        <div id="contactFormResponse" style="margin-bottom: 15px; display: none;"></div>
                        <button type="submit" class="btn-primary">Send</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Zamara Insurance. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <script src="assets/js/main.js"></script>
    <script>
        function scrollToFooter(event) {
            event.preventDefault();
            document.getElementById('footer').scrollIntoView({
                behavior: 'smooth'
            });
        }

        function submitContactForm(event) {
            event.preventDefault();

            const name = document.querySelector('#contactForm input[name="name"]').value;
            const email = document.querySelector('#contactForm input[name="email"]').value;
            const message = document.querySelector('#contactForm textarea[name="message"]').value;

            const responseDiv = document.getElementById('contactFormResponse');

            // Simple validation
            if (!name || !email || !message) {
                responseDiv.style.display = 'block';
                responseDiv.style.color = '#dc3545';
                responseDiv.innerHTML = 'Please fill in all fields.';
                return false;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                responseDiv.style.display = 'block';
                responseDiv.style.color = '#dc3545';
                responseDiv.innerHTML = 'Please enter a valid email address.';
                return false;
            }

            // Simulate form submission success
            responseDiv.style.display = 'block';
            responseDiv.style.color = '#28a745';
            responseDiv.innerHTML = 'Thank you for your message! We will get back to you soon.';

            // Clear form
            document.querySelector('#contactForm input[name="name"]').value = '';
            document.querySelector('#contactForm input[name="email"]').value = '';
            document.querySelector('#contactForm textarea[name="message"]').value = '';

            return false;
        }
    </script>
</body>
</html>