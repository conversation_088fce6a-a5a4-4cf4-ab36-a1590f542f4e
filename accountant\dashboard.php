<?php
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

// Check if the user has accountant role
$is_accountant = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "System Accountant") {
        $is_accountant = true;
        break;
    }
}

// If not accountant, redirect to regular dashboard
if(!$is_accountant) {
    header("location: ../dashboard.php");
    exit;
}

$page_title = "Accountant Dashboard";
$page_description = "Manage financial records and payments.";

require_once '../includes/db_config.php';

// Get payment statistics
$query = "SELECT COUNT(*) as total_payments, SUM(amount) as total_amount FROM payments";
$stmt = $conn->prepare($query);
$stmt->execute();
$payment_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get recent payments
$query = "SELECT p.payment_id, p.payment_number, p.amount, p.payment_date, p.payment_method, p.status,
          cp.policy_number, u.first_name, u.last_name
          FROM payments p
          JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
          JOIN users u ON cp.customer_id = u.user_id
          ORDER BY p.payment_date DESC
          LIMIT 10";
$stmt = $conn->prepare($query);
$stmt->execute();
$recent_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Zamara' : 'Zamara - Insurance & Financial Services'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Zamara offers comprehensive insurance and financial services to individuals and businesses.'; ?>">

    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .accountant-header {
            background-color: #343a40;
            color: white;
        }

        .accountant-sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 70px);
        }

        .accountant-sidebar a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }

        .accountant-sidebar a:hover, .accountant-sidebar a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .accountant-content {
            padding: 20px;
        }

        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin-bottom: 10px;
            color: #343a40;
        }

        .stat-card p {
            font-size: 24px;
            font-weight: 700;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
    </style>
</head>
<body>

    <header class="accountant-header">
        <div class="container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
            <div>
                <h2>Zamara Accountant</h2>
            </div>

            <div style="display: flex; align-items: center;">
                <span style="margin-right: 15px;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                <a href="../logout.php" style="color: white; text-decoration: none; background-color: #dc3545; padding: 5px 10px; border-radius: 4px;">Logout</a>
            </div>
        </div>
    </header>

    <div style="display: flex;">

        <div class="accountant-sidebar" style="width: 250px; padding: 20px;">
            <h3 style="margin-bottom: 20px;">Accountant Menu</h3>

            <nav>
                <a href="dashboard.php" class="active">Dashboard</a>
                <a href="payments.php">Manage Payments</a>
                <a href="invoices.php">Invoices</a>
                <a href="reports.php">Financial Reports</a>
                <a href="settings.php">Settings</a>
                <a href="../index.php">View Website</a>
            </nav>
        </div>

        <div class="accountant-content" style="flex: 1;">
            <h1 style="margin-bottom: 20px;">Accountant Dashboard</h1>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="border-top: 4px solid #28a745;">
                    <h3>Total Payments</h3>
                    <p style="color: #28a745;"><?php echo $payment_stats['total_payments']; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #0056b3;">
                    <h3>Total Amount</h3>
                    <p style="color: #0056b3;">KSH <?php echo number_format($payment_stats['total_amount'], 2); ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #ffc107;">
                    <h3>Pending Payments</h3>
                    <p style="color: #ffc107;">0</p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #dc3545;">
                    <h3>Failed Payments</h3>
                    <p style="color: #dc3545;">0</p>
                </div>
            </div>

            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 30px;">
                <h2 style="margin-bottom: 20px;">Recent Payments</h2>

                <?php if(count($recent_payments) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Payment #</th>
                                <th>Customer</th>
                                <th>Policy #</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($recent_payments as $payment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($payment['payment_number']); ?></td>
                                    <td><?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?></td>
                                    <td><?php echo htmlspecialchars($payment['policy_number']); ?></td>
                                    <td>KSH <?php echo number_format($payment['amount'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                    <td>
                                        <span style="padding: 3px 8px; border-radius: 4px; font-size: 12px;
                                            <?php
                                            switch($payment['status']) {
                                                case 'Completed':
                                                    echo 'background-color: #d4edda; color: #155724;';
                                                    break;
                                                case 'Pending':
                                                    echo 'background-color: #fff3cd; color: #856404;';
                                                    break;
                                                case 'Failed':
                                                    echo 'background-color: #f8d7da; color: #721c24;';
                                                    break;
                                                default:
                                                    echo 'background-color: #e2e3e5; color: #383d41;';
                                            }
                                            ?>">
                                            <?php echo htmlspecialchars($payment['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="view_payment.php?id=<?php echo $payment['payment_id']; ?>" style="color: #0056b3; margin-right: 10px;">View</a>
                                        <a href="receipt.php?payment_id=<?php echo $payment['payment_id']; ?>" style="color: #28a745;" target="_blank">Receipt</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No payments found.</p>
                <?php endif; ?>

                <div style="margin-top: 20px;">
                    <a href="payments.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Payments</a>
                </div>
            </div>

            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h2 style="margin-bottom: 20px;">Quick Actions</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <a href="record_payment.php" style="background-color: #28a745; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Record Payment</h3>
                    </a>

                    <a href="invoices.php" style="background-color: #0056b3; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Manage Invoices</h3>
                    </a>

                    <a href="reports.php" style="background-color: #ffc107; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Financial Reports</h3>
                    </a>

                    <a href="settings.php" style="background-color: #6c757d; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Settings</h3>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <footer style="background-color: #343a40; color: white; padding: 20px 0; text-align: center; margin-top: 30px;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Zamara Accountant Panel. All Rights Reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
</body>
</html>
