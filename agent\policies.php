<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_agent = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Insurance Agent") {
        $is_agent = true;
        break;
    }
}

if(!$is_agent) {
    header("location: ../dashboard.php");
    exit;
}

$page_title = "Manage Policies";
$page_description = "View and manage insurance policies.";

require_once '../includes/db_config.php';


$search = "";
$policy_type = "";
$success_message = "";
$error_message = "";


if(isset($_GET['search']) || isset($_GET['policy_type'])) {
    $search = isset($_GET['search']) ? trim($_GET['search']) : "";
    $policy_type = isset($_GET['policy_type']) ? trim($_GET['policy_type']) : "";
}


$query = "SELECT policy_type_id, type_name FROM policy_types ORDER BY type_name";
$stmt = $conn->prepare($query);
$stmt->execute();
$policy_types = $stmt->fetchAll(PDO::FETCH_ASSOC);


$query = "SELECT p.policy_id, p.policy_name, pt.type_name, p.duration, p.premium_amount, p.coverage_amount, p.created_at,
          (SELECT COUNT(*) FROM customer_policies WHERE policy_id = p.policy_id) as customer_count
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE 1=1";

$params = array();

if(!empty($search)) {
    $query .= " AND (p.policy_name LIKE :search OR p.description LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if(!empty($policy_type)) {
    $query .= " AND pt.policy_type_id = :policy_type";
    $params[':policy_type'] = $policy_type;
}

$query .= " ORDER BY p.created_at DESC";

$stmt = $conn->prepare($query);
foreach($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$policies = $stmt->fetchAll(PDO::FETCH_ASSOC);


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Zamara' : 'Zamara - Insurance & Financial Services'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Zamara offers comprehensive insurance and financial services to individuals and businesses.'; ?>">

    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .agent-header {
            background-color: #28a745;
            color: white;
        }

        .agent-sidebar {
            background-color: #f8f9fa;
            min-height: calc(100vh - 70px);
            border-right: 1px solid #ddd;
        }

        .agent-sidebar a {
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }

        .agent-sidebar a:hover, .agent-sidebar a.active {
            background-color: #28a745;
            color: white;
        }

        .agent-content {
            padding: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
    </style>
</head>
<body>

    <header class="agent-header">
        <div class="container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
            <div>
                <h2>Zamara Insurance Agent</h2>
            </div>

            <div style="display: flex; align-items: center;">
                <span style="margin-right: 15px;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                <a href="../logout.php" style="color: white; text-decoration: none; background-color: rgba(0,0,0,0.2); padding: 5px 10px; border-radius: 4px;">Logout</a>
            </div>
        </div>
    </header>

    <div style="display: flex;">

        <div class="agent-sidebar" style="width: 250px; padding: 20px;">
            <h3 style="margin-bottom: 20px;">Agent Menu</h3>

            <nav>
                <a href="dashboard.php">Dashboard</a>
                <a href="policies.php" class="active">Manage Policies</a>
                <a href="create_policy.php">Create New Policy</a>
                <a href="customers.php">Manage Customers</a>
                <a href="claims.php">Manage Claims</a>
                <a href="../index.php">View Website</a>
            </nav>
        </div>


        <div class="agent-content" style="flex: 1;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1 style="margin: 0;">Manage Policies</h1>
                <a href="create_policy.php" style="background-color: #28a745; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Create New Policy</a>
            </div>

            <?php if(!empty($success_message)): ?>
                <div style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if(!empty($error_message)): ?>
                <div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 20px;">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="get" style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <div style="flex: 1;">
                        <label for="search" style="display: block; margin-bottom: 5px; font-weight: 500;">Search</label>
                        <input type="text" id="search" name="search" value="<?php echo $search; ?>" placeholder="Search by name or description" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div style="flex: 1;">
                        <label for="policy_type" style="display: block; margin-bottom: 5px; font-weight: 500;">Policy Type</label>
                        <select id="policy_type" name="policy_type" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">All Types</option>
                            <?php foreach($policy_types as $type): ?>
                                <option value="<?php echo $type['policy_type_id']; ?>" <?php echo ($policy_type == $type['policy_type_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div style="align-self: flex-end;">
                        <button type="submit" style="background-color: #0056b3; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500;">Filter</button>
                        <a href="policies.php" style="display: inline-block; margin-left: 10px; padding: 10px 20px; background-color: #6c757d; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Reset</a>
                    </div>
                </form>
            </div>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <?php if(count($policies) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Policy Name</th>
                                <th>Type</th>
                                <th>Duration</th>
                                <th>Premium</th>
                                <th>Coverage</th>
                                <th>Customers</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($policies as $policy): ?>
                                <tr>
                                    <td><?php echo $policy['policy_id']; ?></td>
                                    <td><?php echo htmlspecialchars($policy['policy_name']); ?></td>
                                    <td><?php echo htmlspecialchars($policy['type_name']); ?></td>
                                    <td><?php echo htmlspecialchars($policy['duration']); ?></td>
                                    <td>$<?php echo number_format($policy['premium_amount'], 2); ?></td>
                                    <td>$<?php echo number_format($policy['coverage_amount'], 2); ?></td>
                                    <td><?php echo $policy['customer_count']; ?></td>
                                    <td><?php echo date('M d, Y', strtotime($policy['created_at'])); ?></td>
                                    <td>
                                        <a href="view_policy.php?id=<?php echo $policy['policy_id']; ?>" style="color: #0056b3; margin-right: 10px;">View</a>
                                        <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" style="color: #28a745; margin-right: 10px;">Edit</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No policies found.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>


    <footer style="background-color: #f8f9fa; color: #333; padding: 20px 0; text-align: center; margin-top: 30px; border-top: 1px solid #ddd;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Zamara Insurance. All Rights Reserved.</p>
        </div>
    </footer>


    <script src="../assets/js/main.js"></script>
</body>
</html>
