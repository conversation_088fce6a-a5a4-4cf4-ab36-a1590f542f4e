<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle form submissions
if($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['add_role'])) {
        $role_name = trim($_POST['role_name']);
        $description = trim($_POST['description']);
        
        if(!empty($role_name)) {
            try {
                $stmt = $conn->prepare("INSERT INTO roles (role_name, description) VALUES (:role_name, :description)");
                $stmt->bindParam(':role_name', $role_name);
                $stmt->bindParam(':description', $description);
                
                if($stmt->execute()) {
                    $success_message = "Role added successfully!";
                } else {
                    $error_message = "Error adding role.";
                }
            } catch(PDOException $e) {
                if($e->getCode() == 23000) {
                    $error_message = "Role name already exists.";
                } else {
                    $error_message = "Error: " . $e->getMessage();
                }
            }
        } else {
            $error_message = "Role name is required.";
        }
    }
    
    if(isset($_POST['delete_role'])) {
        $role_id = $_POST['role_id'];
        
        try {
            // Check if role is assigned to any users
            $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_roles WHERE role_id = :role_id");
            $check_stmt->bindParam(':role_id', $role_id);
            $check_stmt->execute();
            $user_count = $check_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if($user_count > 0) {
                $error_message = "Cannot delete role. It is assigned to $user_count user(s).";
            } else {
                $stmt = $conn->prepare("DELETE FROM roles WHERE role_id = :role_id");
                $stmt->bindParam(':role_id', $role_id);
                
                if($stmt->execute()) {
                    $success_message = "Role deleted successfully!";
                } else {
                    $error_message = "Error deleting role.";
                }
            }
        } catch(PDOException $e) {
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Get all roles with user counts
$query = "SELECT r.*, COUNT(ur.user_id) as user_count 
          FROM roles r 
          LEFT JOIN user_roles ur ON r.role_id = ur.role_id 
          GROUP BY r.role_id 
          ORDER BY r.role_name";
$stmt = $conn->prepare($query);
$stmt->execute();
$roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Roles";
$page_description = "Create and manage user roles in the system.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #6f42c1; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-container { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #6f42c1; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #6f42c1; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .role-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #6f42c1; }
        .user-count { background-color: #e9ecef; padding: 4px 8px; border-radius: 12px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <div class="content">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- Add New Role Form -->
            <div class="form-container">
                <h2>Add New Role</h2>
                <form method="post">
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 15px; align-items: end;">
                        <div class="form-group">
                            <label>Role Name *</label>
                            <input type="text" name="role_name" required placeholder="e.g., Manager">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <input type="text" name="description" placeholder="Role description">
                        </div>
                        <div class="form-group">
                            <button type="submit" name="add_role" class="btn btn-primary">Add Role</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Roles List -->
            <h2>Existing Roles (<?php echo count($roles); ?>)</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach($roles as $role): ?>
                <div class="role-card">
                    <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 10px;">
                        <div style="flex: 1;">
                            <h3 style="color: #6f42c1; margin-bottom: 5px;"><?php echo htmlspecialchars($role['role_name']); ?></h3>
                            <p style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">
                                <?php echo htmlspecialchars($role['description'] ?: 'No description'); ?>
                            </p>
                            <span class="user-count"><?php echo $role['user_count']; ?> user(s)</span>
                        </div>
                        <div style="margin-left: 15px;">
                            <?php if($role['user_count'] == 0): ?>
                                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this role?')">
                                    <input type="hidden" name="role_id" value="<?php echo $role['role_id']; ?>">
                                    <button type="submit" name="delete_role" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                </form>
                            <?php else: ?>
                                <span style="color: #6c757d; font-size: 12px;">Cannot delete<br>(has users)</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #6c757d;">
                        Created: <?php echo date('M d, Y', strtotime($role['created_at'])); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Roles Table -->
            <h3 style="margin-top: 40px;">Detailed View</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Role Name</th>
                        <th>Description</th>
                        <th>Users Assigned</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($roles as $role): ?>
                    <tr>
                        <td><?php echo $role['role_id']; ?></td>
                        <td><strong><?php echo htmlspecialchars($role['role_name']); ?></strong></td>
                        <td><?php echo htmlspecialchars($role['description'] ?: 'No description'); ?></td>
                        <td>
                            <span class="user-count"><?php echo $role['user_count']; ?></span>
                            <?php if($role['user_count'] > 0): ?>
                                <a href="view_role_users.php?role_id=<?php echo $role['role_id']; ?>" style="margin-left: 10px; color: #6f42c1; text-decoration: none; font-size: 12px;">View Users</a>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($role['created_at'])); ?></td>
                        <td>
                            <?php if($role['user_count'] == 0): ?>
                                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this role?')">
                                    <input type="hidden" name="role_id" value="<?php echo $role['role_id']; ?>">
                                    <button type="submit" name="delete_role" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                </form>
                            <?php else: ?>
                                <span style="color: #6c757d; font-size: 12px;">Cannot delete</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <div style="margin-top: 30px; padding: 15px; background-color: #e9ecef; border-radius: 8px;">
                <h4>Role Management Tips:</h4>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>Roles define what users can do in the system</li>
                    <li>You can only delete roles that have no users assigned</li>
                    <li>Default roles (Administrator, Agent, Customer, Accountant) are recommended</li>
                    <li>Be descriptive with role names and descriptions</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
