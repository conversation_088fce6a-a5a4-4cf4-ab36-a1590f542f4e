<?php
session_start();

$page_title = "Browse Policies";
$page_description = "Explore our range of insurance policies and find the right coverage for your needs.";

require_once 'includes/db_config.php';
$policy_types = ['Motor', 'Health', 'Life', 'Business'];
foreach ($policy_types as $type_name) {
    $check_query = "SELECT policy_type_id FROM policy_types WHERE type_name = :type_name";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(":type_name", $type_name);
    $check_stmt->execute();

    if($check_stmt->rowCount() == 0) {
        $insert_query = "INSERT INTO policy_types (type_name, description) VALUES (:type_name, :description)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bindParam(":type_name", $type_name);
        $insert_stmt->bindValue(":description", "Insurance for " . $type_name);
        $insert_stmt->execute();
    }
}


$policies = [

    [
        'policy_id' => 1,
        'policy_name' => 'Comprehensive Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Provides coverage for damage to your vehicle as well as third-party liability.',
        'duration' => '12 months',
        'premium_amount' => 15000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers damage to your vehicle due to accidents, theft, fire, and natural disasters. It also covers third-party liability for bodily injury and property damage.'
    ],
    [
        'policy_id' => 5,
        'policy_name' => 'Third-Party Motor Insurance',
        'type_name' => 'Motor',
        'description' => 'Basic coverage for third-party liability only, as required by law.',
        'duration' => '12 months',
        'premium_amount' => 7500,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers third-party liability for bodily injury and property damage. It does not cover damage to your own vehicle.'
    ],
    [
        'policy_id' => 6,
        'policy_name' => 'Commercial Vehicle Insurance',
        'type_name' => 'Motor',
        'description' => 'Specialized coverage for commercial vehicles including trucks, vans, and taxis.',
        'duration' => '12 months',
        'premium_amount' => 20000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers commercial vehicles for damage, theft, and third-party liability. Additional coverage for goods in transit is available.'
    ],


    [
        'policy_id' => 2,
        'policy_name' => 'Basic Health Insurance',
        'type_name' => 'Health',
        'description' => 'Covers basic medical expenses including hospitalization, surgery, and prescription drugs.',
        'duration' => '12 months',
        'premium_amount' => 12000,
        'coverage_amount' => 500000,
        'terms_conditions' => 'This policy covers hospitalization, surgery, and prescription drugs. Pre-existing conditions may have a waiting period of 6 months. Dental and vision care are not covered.'
    ],
    [
        'policy_id' => 7,
        'policy_name' => 'Premium Health Insurance',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage including dental, vision, and mental health services.',
        'duration' => '12 months',
        'premium_amount' => 24000,
        'coverage_amount' => 1000000,
        'terms_conditions' => 'This policy covers all medical expenses including hospitalization, surgery, prescription drugs, dental, vision, and mental health services. Pre-existing conditions have a waiting period of 3 months.'
    ],
    [
        'policy_id' => 8,
        'policy_name' => 'Family Health Plan',
        'type_name' => 'Health',
        'description' => 'Comprehensive health coverage for the entire family with special benefits for children.',
        'duration' => '12 months',
        'premium_amount' => 36000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy covers all medical expenses for up to 4 family members. Special benefits include pediatric care, maternity coverage, and preventive health checkups.'
    ],


    [
        'policy_id' => 3,
        'policy_name' => 'Term Life Insurance',
        'type_name' => 'Life',
        'description' => 'Provides financial protection to your beneficiaries in case of your death during the policy term.',
        'duration' => '10 years',
        'premium_amount' => 8000,
        'coverage_amount' => 2000000,
        'terms_conditions' => 'This policy provides a death benefit to your beneficiaries if you die during the policy term. Suicide is not covered in the first two years of the policy.'
    ],
    [
        'policy_id' => 9,
        'policy_name' => 'Whole Life Insurance',
        'type_name' => 'Life',
        'description' => 'Lifetime coverage with a cash value component that grows over time.',
        'duration' => 'Lifetime',
        'premium_amount' => 15000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy provides lifetime coverage with a cash value component that grows over time. You can borrow against the cash value or surrender the policy for its cash value.'
    ],
    [
        'policy_id' => 10,
        'policy_name' => 'Endowment Policy',
        'type_name' => 'Life',
        'description' => 'Combines life insurance with savings, providing a lump sum payment at the end of the term.',
        'duration' => '15 years',
        'premium_amount' => 12000,
        'coverage_amount' => 1500000,
        'terms_conditions' => 'This policy provides life insurance coverage and a lump sum payment at the end of the term. Premiums are higher than term life insurance but provide a guaranteed return.'
    ],


    [
        'policy_id' => 4,
        'policy_name' => 'Business Property Insurance',
        'type_name' => 'Business',
        'description' => 'Protects your business property against damage or loss due to fire, theft, and natural disasters.',
        'duration' => '12 months',
        'premium_amount' => 25000,
        'coverage_amount' => 5000000,
        'terms_conditions' => 'This policy covers damage to your business property due to fire, theft, and natural disasters. Business interruption coverage is included for up to 3 months.'
    ],
    [
        'policy_id' => 11,
        'policy_name' => 'Professional Liability Insurance',
        'type_name' => 'Business',
        'description' => 'Protects professionals against claims of negligence or failure to perform their professional duties.',
        'duration' => '12 months',
        'premium_amount' => 18000,
        'coverage_amount' => 3000000,
        'terms_conditions' => 'This policy covers legal costs and damages awarded in claims of professional negligence. It does not cover criminal prosecution or certain liabilities that may be specified in the policy.'
    ],
    [
        'policy_id' => 12,
        'policy_name' => 'Workers\' Compensation Insurance',
        'type_name' => 'Business',
        'description' => 'Provides coverage for employee injuries or illnesses that occur as a result of their job.',
        'duration' => '12 months',
        'premium_amount' => 30000,
        'coverage_amount' => 4000000,
        'terms_conditions' => 'This policy covers medical expenses, rehabilitation costs, and lost wages for employees who are injured or become ill as a result of their job. It also provides death benefits to dependents of workers who are killed on the job.'
    ]
];


$policies_by_type = [];
foreach ($policies as $policy) {
    $type = $policy['type_name'];
    if (!isset($policies_by_type[$type])) {
        $policies_by_type[$type] = [];
    }
    $policies_by_type[$type][] = $policy;
}


include_once 'includes/header.php';
?>

<section style="padding: 80px 0; background-color: #f8f9fa;">
    <div class="container">
        <div style="text-align: center; max-width: 800px; margin: 0 auto;">
            <h1 style="margin-bottom: 20px; color: #0056b3;">Insurance Policies</h1>
            <p style="margin-bottom: 40px; font-size: 18px; color: #6c757d;">Explore our range of insurance policies designed to protect what matters most to you. From motor and health to life and business insurance, we have you covered.</p>
        </div>

        <div style="margin-bottom: 40px;">
            <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 15px;">
                <a href="#motor" style="padding: 10px 20px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Motor Insurance</a>
                <a href="#health" style="padding: 10px 20px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Health Insurance</a>
                <a href="#life" style="padding: 10px 20px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Life Insurance</a>
                <a href="#business" style="padding: 10px 20px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none; font-weight: 500;">Business Insurance</a>
            </div>
        </div>

        <?php foreach ($policies_by_type as $type => $type_policies): ?>
            <div id="<?php echo strtolower($type); ?>" style="margin-bottom: 60px;">
                <h2 style="margin-bottom: 30px; color: #0056b3; text-align: center;"><?php echo htmlspecialchars($type); ?> Insurance</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 30px;">
                    <?php foreach ($type_policies as $policy): ?>
                        <div style="background-color: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);">
                            <div style="padding: 20px;">
                                <h3 style="margin-bottom: 15px; color: #0056b3;"><?php echo htmlspecialchars($policy['policy_name']); ?></h3>
                                <p style="margin-bottom: 15px; color: #6c757d; height: 60px; overflow: hidden;"><?php echo htmlspecialchars($policy['description']); ?></p>
                                <div style="margin-bottom: 15px;">
                                    <p style="margin-bottom: 5px;"><strong>Premium:</strong> KSH <?php echo number_format($policy['premium_amount'], 2); ?></p>
                                    <p style="margin-bottom: 5px;"><strong>Coverage:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>
                                    <p><strong>Duration:</strong> <?php echo htmlspecialchars($policy['duration']); ?></p>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <a href="policy_details.php?id=<?php echo $policy['policy_id']; ?>" style="flex: 1; padding: 10px 0; background-color: #0056b3; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-weight: 500;">View Details</a>
                                    <a href="apply_policy.php?id=<?php echo $policy['policy_id']; ?>" style="flex: 1; padding: 10px 0; background-color: #28a745; color: white; text-align: center; border-radius: 4px; text-decoration: none; font-weight: 500;">Apply Now</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <div style="text-align: center; margin-top: 40px;">
            <h2 style="margin-bottom: 20px; color: #0056b3;">Need Help Choosing the Right Policy?</h2>
            <p style="margin-bottom: 30px; font-size: 18px; color: #6c757d;">Our insurance experts are here to help you find the perfect coverage for your needs.</p>
            <a href="#footer" onclick="scrollToFooter(event)" style="display: inline-block; padding: 12px 30px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none; font-weight: 500; font-size: 18px;">Contact Us</a>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>