<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$customer_id = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;

if($customer_id <= 0) {
    header("location: customers.php");
    exit;
}

$success_message = $error_message = "";

// Handle policy assignment
if($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['assign_policy'])) {
    $policy_id = $_POST['policy_id'];
    $start_date = $_POST['start_date'];
    $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
    $notes = trim($_POST['notes']);
    
    try {
        $conn->beginTransaction();
        
        // Generate policy number
        $policy_number = 'POL-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        
        // Check if policy number already exists
        $check_stmt = $conn->prepare("SELECT COUNT(*) FROM customer_policies WHERE policy_number = :policy_number");
        $check_stmt->bindParam(':policy_number', $policy_number);
        $check_stmt->execute();
        
        while($check_stmt->fetchColumn() > 0) {
            $policy_number = 'POL-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
            $check_stmt->bindParam(':policy_number', $policy_number);
            $check_stmt->execute();
        }
        
        // Insert customer policy
        $stmt = $conn->prepare("INSERT INTO customer_policies (customer_id, policy_id, policy_number, start_date, end_date, status, notes, assigned_by) VALUES (:customer_id, :policy_id, :policy_number, :start_date, :end_date, 'Active', :notes, :assigned_by)");
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->bindParam(':policy_id', $policy_id);
        $stmt->bindParam(':policy_number', $policy_number);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':assigned_by', $_SESSION['user_id']);
        
        if($stmt->execute()) {
            $conn->commit();
            $success_message = "Policy assigned successfully! Policy Number: $policy_number";
        } else {
            $conn->rollback();
            $error_message = "Error assigning policy.";
        }
    } catch(PDOException $e) {
        $conn->rollback();
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get customer details
$customer_query = "SELECT u.*, 
                          COUNT(cp.customer_policy_id) as total_policies
                   FROM users u
                   JOIN user_roles ur ON u.user_id = ur.user_id
                   JOIN roles r ON ur.role_id = r.role_id
                   LEFT JOIN customer_policies cp ON u.user_id = cp.customer_id
                   WHERE u.user_id = :customer_id AND r.role_name = 'Customer'
                   GROUP BY u.user_id";

$customer_stmt = $conn->prepare($customer_query);
$customer_stmt->bindParam(':customer_id', $customer_id);
$customer_stmt->execute();
$customer = $customer_stmt->fetch(PDO::FETCH_ASSOC);

if(!$customer) {
    header("location: customers.php");
    exit;
}

// Get available policies
$policies_query = "SELECT p.*, pt.type_name 
                   FROM policies p
                   JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
                   WHERE p.status = 'Active'
                   ORDER BY pt.type_name, p.policy_name";

$policies_stmt = $conn->prepare($policies_query);
$policies_stmt->execute();
$available_policies = $policies_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get customer's existing policies to avoid duplicates
$existing_query = "SELECT policy_id FROM customer_policies WHERE customer_id = :customer_id AND status IN ('Active', 'Suspended')";
$existing_stmt = $conn->prepare($existing_query);
$existing_stmt->bindParam(':customer_id', $customer_id);
$existing_stmt->execute();
$existing_policy_ids = $existing_stmt->fetchAll(PDO::FETCH_COLUMN);

$page_title = "Assign Policy to " . $customer['first_name'] . " " . $customer['last_name'];
$page_description = "Assign a new insurance policy to the customer.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #218838; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .customer-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #28a745; }
        .policy-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #ddd; cursor: pointer; transition: all 0.3s ease; }
        .policy-card:hover { border-color: #28a745; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .policy-card.selected { border-color: #28a745; background-color: #d4edda; }
        .policy-card.disabled { opacity: 0.5; cursor: not-allowed; background-color: #e9ecef; }
        .policy-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-top: 10px; }
        .detail-item { }
        .detail-label { font-size: 12px; color: #6c757d; font-weight: 600; }
        .detail-value { font-size: 14px; }
        .error { color: #dc3545; font-size: 14px; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="view_customer.php?id=<?php echo $customer['user_id']; ?>" class="back-link">← Back to Customer Profile</a>
        
        <?php if(!empty($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if(!empty($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Customer Information -->
        <div class="customer-info">
            <h3>Customer Information</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>
                    <div class="detail-label">CUSTOMER NAME</div>
                    <div class="detail-value"><strong><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></strong></div>
                </div>
                <div>
                    <div class="detail-label">EMAIL</div>
                    <div class="detail-value"><?php echo htmlspecialchars($customer['email']); ?></div>
                </div>
                <div>
                    <div class="detail-label">PHONE</div>
                    <div class="detail-value"><?php echo htmlspecialchars($customer['phone'] ?: 'Not provided'); ?></div>
                </div>
                <div>
                    <div class="detail-label">EXISTING POLICIES</div>
                    <div class="detail-value"><?php echo $customer['total_policies']; ?> policies</div>
                </div>
            </div>
        </div>

        <!-- Policy Assignment Form -->
        <div class="form-container">
            <h2>Select Policy to Assign</h2>
            
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . "?customer_id=" . $customer_id; ?>" method="post">
                <div class="form-group">
                    <label>Available Policies *</label>
                    <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                        <?php foreach($available_policies as $policy): ?>
                            <?php 
                            $is_assigned = in_array($policy['policy_id'], $existing_policy_ids);
                            $card_class = $is_assigned ? 'policy-card disabled' : 'policy-card';
                            ?>
                            <div class="<?php echo $card_class; ?>" <?php if(!$is_assigned): ?>onclick="selectPolicy(<?php echo $policy['policy_id']; ?>)"<?php endif; ?>>
                                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                    <div>
                                        <h4><?php echo htmlspecialchars($policy['policy_name']); ?></h4>
                                        <p style="color: #6c757d; font-size: 14px;"><?php echo htmlspecialchars($policy['type_name']); ?></p>
                                        <?php if($is_assigned): ?>
                                            <span style="color: #dc3545; font-size: 12px; font-weight: 600;">Already Assigned</span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <?php if(!$is_assigned): ?>
                                            <input type="radio" name="policy_id" value="<?php echo $policy['policy_id']; ?>" required style="width: auto;">
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="policy-details">
                                    <div class="detail-item">
                                        <div class="detail-label">PREMIUM</div>
                                        <div class="detail-value">KSH <?php echo number_format($policy['premium_amount'], 2); ?></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">COVERAGE</div>
                                        <div class="detail-value">KSH <?php echo number_format($policy['coverage_amount'], 2); ?></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">DURATION</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($policy['duration'] ?: 'Flexible'); ?></div>
                                    </div>
                                </div>
                                
                                <?php if($policy['description']): ?>
                                <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #ddd;">
                                    <div class="detail-label">DESCRIPTION</div>
                                    <div class="detail-value" style="font-size: 13px;"><?php echo htmlspecialchars($policy['description']); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Start Date *</label>
                        <input type="date" name="start_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>End Date (Optional)</label>
                        <input type="date" name="end_date" min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                        <small style="color: #6c757d;">Leave empty for ongoing coverage</small>
                    </div>
                </div>

                <div class="form-group">
                    <label>Assignment Notes</label>
                    <textarea name="notes" rows="3" placeholder="Add any notes about this policy assignment..."></textarea>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button type="submit" name="assign_policy" class="btn">Assign Policy</button>
                    <a href="view_customer.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                </div>
            </form>
        </div>

        <?php if(empty($available_policies)): ?>
            <div class="content">
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No Policies Available</h3>
                    <p>There are no active policies available for assignment.</p>
                    <p>Please contact an administrator to create new policies.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function selectPolicy(policyId) {
            // Remove selected class from all cards
            document.querySelectorAll('.policy-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Select the radio button
            const radio = event.currentTarget.querySelector('input[type="radio"]');
            if(radio) {
                radio.checked = true;
            }
        }

        // Auto-select if radio is clicked directly
        document.querySelectorAll('input[type="radio"][name="policy_id"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.policy-card').forEach(card => {
                    card.classList.remove('selected');
                });
                this.closest('.policy-card').classList.add('selected');
            });
        });

        // Validate dates
        document.querySelector('input[name="end_date"]').addEventListener('change', function() {
            const startDate = document.querySelector('input[name="start_date"]').value;
            const endDate = this.value;
            
            if(startDate && endDate && new Date(endDate) <= new Date(startDate)) {
                alert('End date must be after start date');
                this.value = '';
            }
        });
    </script>
</body>
</html>
