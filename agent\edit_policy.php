<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$policy_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$success_message = $error_message = "";

if($policy_id <= 0) {
    header("location: policies.php");
    exit;
}

// Get policy details
$query = "SELECT p.*, pt.type_name FROM policies p 
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id 
          WHERE p.policy_id = :policy_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':policy_id', $policy_id);
$stmt->execute();
$policy = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$policy) {
    header("location: policies.php");
    exit;
}

// Handle form submission
if($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_policy'])) {
    $policy_name = trim($_POST['policy_name']);
    $policy_type_id = $_POST['policy_type_id'];
    $description = trim($_POST['description']);
    $duration = trim($_POST['duration']);
    $premium_amount = $_POST['premium_amount'];
    $coverage_amount = $_POST['coverage_amount'];
    $terms_conditions = trim($_POST['terms_conditions']);
    $status = $_POST['status'];
    
    try {
        $update_query = "UPDATE policies SET 
                        policy_name = :policy_name,
                        policy_type_id = :policy_type_id,
                        description = :description,
                        duration = :duration,
                        premium_amount = :premium_amount,
                        coverage_amount = :coverage_amount,
                        terms_conditions = :terms_conditions,
                        status = :status,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE policy_id = :policy_id";
        
        $stmt = $conn->prepare($update_query);
        $stmt->bindParam(':policy_name', $policy_name);
        $stmt->bindParam(':policy_type_id', $policy_type_id);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':duration', $duration);
        $stmt->bindParam(':premium_amount', $premium_amount);
        $stmt->bindParam(':coverage_amount', $coverage_amount);
        $stmt->bindParam(':terms_conditions', $terms_conditions);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':policy_id', $policy_id);
        
        if($stmt->execute()) {
            $success_message = "Policy updated successfully!";
            // Refresh policy data
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':policy_id', $policy_id);
            $stmt->execute();
            $policy = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $error_message = "Error updating policy.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get policy types
$types_query = "SELECT * FROM policy_types ORDER BY type_name";
$types_stmt = $conn->prepare($types_query);
$types_stmt->execute();
$policy_types = $types_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Edit Policy - " . $policy['policy_name'];
$page_description = "Update policy information and settings.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .form-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { background-color: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #218838; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .policy-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="view_policy.php?id=<?php echo $policy_id; ?>" class="back-link">← Back to Policy Details</a>
        
        <div class="form-container">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                    <div style="margin-top: 10px;">
                        <a href="view_policy.php?id=<?php echo $policy_id; ?>" class="btn btn-primary">View Policy</a>
                        <a href="policies.php" class="btn btn-secondary">Back to Policies</a>
                    </div>
                </div>
            <?php else: ?>
                <?php if(!empty($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <!-- Current Policy Information -->
                <div class="policy-info">
                    <h3>Current Policy Information</h3>
                    <p><strong>Policy ID:</strong> <?php echo $policy['policy_id']; ?></p>
                    <p><strong>Current Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                    <p><strong>Created:</strong> <?php echo date('M d, Y H:i', strtotime($policy['created_at'])); ?></p>
                    <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($policy['updated_at'])); ?></p>
                </div>

                <!-- Edit Form -->
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . "?id=" . $policy_id; ?>" method="post">
                    <h3 style="margin-bottom: 20px; color: #28a745;">Update Policy Details</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Policy Name *</label>
                            <input type="text" name="policy_name" value="<?php echo htmlspecialchars($policy['policy_name']); ?>" required>
                        </div>
                        <div class="form-group">
                            <label>Policy Type *</label>
                            <select name="policy_type_id" required>
                                <?php foreach($policy_types as $type): ?>
                                    <option value="<?php echo $type['policy_type_id']; ?>" 
                                            <?php echo ($type['policy_type_id'] == $policy['policy_type_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($type['type_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Policy Description</label>
                        <textarea name="description" rows="4" placeholder="Describe what this policy covers..."><?php echo htmlspecialchars($policy['description']); ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Duration *</label>
                            <select name="duration" required>
                                <option value="6 months" <?php echo ($policy['duration'] == '6 months') ? 'selected' : ''; ?>>6 months</option>
                                <option value="12 months" <?php echo ($policy['duration'] == '12 months') ? 'selected' : ''; ?>>12 months</option>
                                <option value="24 months" <?php echo ($policy['duration'] == '24 months') ? 'selected' : ''; ?>>24 months</option>
                                <option value="36 months" <?php echo ($policy['duration'] == '36 months') ? 'selected' : ''; ?>>36 months</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Status *</label>
                            <select name="status" required>
                                <option value="Active" <?php echo ($policy['status'] == 'Active') ? 'selected' : ''; ?>>Active</option>
                                <option value="Inactive" <?php echo ($policy['status'] == 'Inactive') ? 'selected' : ''; ?>>Inactive</option>
                                <option value="Discontinued" <?php echo ($policy['status'] == 'Discontinued') ? 'selected' : ''; ?>>Discontinued</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Premium Amount (KSH) *</label>
                            <input type="number" name="premium_amount" step="0.01" min="0" 
                                   value="<?php echo $policy['premium_amount']; ?>" required>
                        </div>
                        <div class="form-group">
                            <label>Coverage Amount (KSH) *</label>
                            <input type="number" name="coverage_amount" step="0.01" min="0" 
                                   value="<?php echo $policy['coverage_amount']; ?>" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Terms and Conditions</label>
                        <textarea name="terms_conditions" rows="6" placeholder="Enter the terms and conditions for this policy..."><?php echo htmlspecialchars($policy['terms_conditions']); ?></textarea>
                    </div>

                    <div style="display: flex; gap: 10px;">
                        <button type="submit" name="update_policy" class="btn">Update Policy</button>
                        <a href="view_policy.php?id=<?php echo $policy_id; ?>" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">Cancel</a>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Calculate coverage ratio
        function updateCoverageRatio() {
            const premium = parseFloat(document.querySelector('input[name="premium_amount"]').value) || 0;
            const coverage = parseFloat(document.querySelector('input[name="coverage_amount"]').value) || 0;
            
            if(premium > 0) {
                const ratio = (coverage / premium).toFixed(1);
                console.log('Coverage ratio: ' + ratio + 'x');
            }
        }

        // Add event listeners
        document.querySelector('input[name="premium_amount"]').addEventListener('input', updateCoverageRatio);
        document.querySelector('input[name="coverage_amount"]').addEventListener('input', updateCoverageRatio);

        // Validate form before submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const premium = parseFloat(document.querySelector('input[name="premium_amount"]').value);
            const coverage = parseFloat(document.querySelector('input[name="coverage_amount"]').value);
            
            if(coverage < premium) {
                if(!confirm('Coverage amount is less than premium amount. Continue?')) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
