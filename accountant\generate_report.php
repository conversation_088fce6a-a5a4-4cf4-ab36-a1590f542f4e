<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$report_type = isset($_GET['type']) ? $_GET['type'] : 'monthly';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Set date ranges based on report type
switch($report_type) {
    case 'daily':
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d');
        $report_title = 'Daily Financial Report - ' . date('M d, Y');
        break;
    case 'weekly':
        $start_date = date('Y-m-d', strtotime('monday this week'));
        $end_date = date('Y-m-d', strtotime('sunday this week'));
        $report_title = 'Weekly Financial Report - Week of ' . date('M d, Y', strtotime($start_date));
        break;
    case 'monthly':
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        $report_title = 'Monthly Financial Report - ' . date('F Y');
        break;
    case 'yearly':
        $start_date = date('Y-01-01');
        $end_date = date('Y-12-31');
        $report_title = 'Annual Financial Report - ' . date('Y');
        break;
    case 'custom':
        if(empty($start_date) || empty($end_date)) {
            $start_date = date('Y-m-01');
            $end_date = date('Y-m-t');
        }
        $report_title = 'Custom Financial Report - ' . date('M d, Y', strtotime($start_date)) . ' to ' . date('M d, Y', strtotime($end_date));
        break;
    default:
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        $report_title = 'Monthly Financial Report - ' . date('F Y');
}

try {
    // Payment statistics for the period
    $payments_query = "SELECT 
                        COUNT(*) as total_payments,
                        SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as total_revenue,
                        SUM(CASE WHEN status = 'Pending' THEN amount ELSE 0 END) as pending_amount,
                        AVG(CASE WHEN status = 'Completed' THEN amount ELSE NULL END) as avg_payment,
                        payment_method,
                        COUNT(*) as method_count,
                        SUM(amount) as method_total
                       FROM payments 
                       WHERE DATE(payment_date) BETWEEN :start_date AND :end_date
                       GROUP BY payment_method
                       ORDER BY method_total DESC";
    
    $payments_stmt = $conn->prepare($payments_query);
    $payments_stmt->bindParam(':start_date', $start_date);
    $payments_stmt->bindParam(':end_date', $end_date);
    $payments_stmt->execute();
    $payment_methods = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Overall totals
    $totals_query = "SELECT 
                      COUNT(*) as total_payments,
                      SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as total_revenue,
                      SUM(CASE WHEN status = 'Pending' THEN amount ELSE 0 END) as pending_amount
                     FROM payments 
                     WHERE DATE(payment_date) BETWEEN :start_date AND :end_date";
    
    $totals_stmt = $conn->prepare($totals_query);
    $totals_stmt->bindParam(':start_date', $start_date);
    $totals_stmt->bindParam(':end_date', $end_date);
    $totals_stmt->execute();
    $totals = $totals_stmt->fetch(PDO::FETCH_ASSOC);

    // Daily breakdown
    $daily_query = "SELECT 
                     DATE(payment_date) as payment_day,
                     COUNT(*) as daily_count,
                     SUM(amount) as daily_total
                    FROM payments 
                    WHERE DATE(payment_date) BETWEEN :start_date AND :end_date
                    AND status = 'Completed'
                    GROUP BY DATE(payment_date)
                    ORDER BY payment_day";
    
    $daily_stmt = $conn->prepare($daily_query);
    $daily_stmt->bindParam(':start_date', $start_date);
    $daily_stmt->bindParam(':end_date', $end_date);
    $daily_stmt->execute();
    $daily_breakdown = $daily_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Policy type performance
    $policy_query = "SELECT 
                      pt.type_name,
                      COUNT(p.payment_id) as payment_count,
                      SUM(p.amount) as total_revenue
                     FROM payments p
                     JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
                     JOIN policies pol ON cp.policy_id = pol.policy_id
                     JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
                     WHERE DATE(p.payment_date) BETWEEN :start_date AND :end_date
                     AND p.status = 'Completed'
                     GROUP BY pt.policy_type_id, pt.type_name
                     ORDER BY total_revenue DESC";
    
    $policy_stmt = $conn->prepare($policy_query);
    $policy_stmt->bindParam(':start_date', $start_date);
    $policy_stmt->bindParam(':end_date', $end_date);
    $policy_stmt->execute();
    $policy_performance = $policy_stmt->fetchAll(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    $error_message = "Error generating report: " . $e->getMessage();
}

$page_title = $report_title;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: white; color: #333; }
        .report-container { max-width: 1000px; margin: 20px auto; padding: 40px; }
        .report-header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid #17a2b8; padding-bottom: 20px; }
        .company-name { font-size: 24px; font-weight: bold; color: #17a2b8; margin-bottom: 5px; }
        .report-title { font-size: 20px; color: #333; margin-bottom: 10px; }
        .report-period { font-size: 14px; color: #6c757d; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .summary-card { background-color: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #17a2b8; }
        .summary-number { font-size: 24px; font-weight: bold; color: #17a2b8; }
        .summary-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .section { margin: 40px 0; }
        .section-title { font-size: 18px; font-weight: bold; color: #17a2b8; margin-bottom: 20px; border-bottom: 2px solid #17a2b8; padding-bottom: 5px; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .print-btn { background-color: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 20px auto; display: block; }
        .print-btn:hover { background-color: #138496; }
        .footer { margin-top: 60px; text-align: center; font-size: 12px; color: #6c757d; border-top: 1px solid #ddd; padding-top: 20px; }
        
        @media print {
            .print-btn { display: none; }
            .report-container { margin: 0; padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Report Header -->
        <div class="report-header">
            <div class="company-name">ZAMARA INSURANCE LIMITED</div>
            <div class="report-title"><?php echo $report_title; ?></div>
            <div class="report-period">
                Period: <?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-number"><?php echo $totals['total_payments']; ?></div>
                <div class="summary-label">Total Payments</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">KSH <?php echo number_format($totals['total_revenue'], 0); ?></div>
                <div class="summary-label">Total Revenue</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">KSH <?php echo number_format($totals['pending_amount'], 0); ?></div>
                <div class="summary-label">Pending Amount</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">KSH <?php echo number_format($totals['total_revenue'] / max($totals['total_payments'], 1), 0); ?></div>
                <div class="summary-label">Average Payment</div>
            </div>
        </div>

        <!-- Payment Methods Breakdown -->
        <?php if(!empty($payment_methods)): ?>
        <div class="section">
            <div class="section-title">Payment Methods Performance</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Number of Payments</th>
                        <th>Total Amount</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_method_amount = array_sum(array_column($payment_methods, 'method_total'));
                    foreach($payment_methods as $method): 
                        $percentage = $total_method_amount > 0 ? ($method['method_total'] / $total_method_amount) * 100 : 0;
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($method['payment_method']); ?></td>
                        <td><?php echo $method['method_count']; ?></td>
                        <td>KSH <?php echo number_format($method['method_total'], 2); ?></td>
                        <td><?php echo number_format($percentage, 1); ?>%</td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Daily Breakdown -->
        <?php if(!empty($daily_breakdown)): ?>
        <div class="section">
            <div class="section-title">Daily Revenue Breakdown</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Number of Payments</th>
                        <th>Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($daily_breakdown as $day): ?>
                    <tr>
                        <td><?php echo date('M d, Y', strtotime($day['payment_day'])); ?></td>
                        <td><?php echo $day['daily_count']; ?></td>
                        <td>KSH <?php echo number_format($day['daily_total'], 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Policy Type Performance -->
        <?php if(!empty($policy_performance)): ?>
        <div class="section">
            <div class="section-title">Revenue by Policy Type</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Policy Type</th>
                        <th>Number of Payments</th>
                        <th>Total Revenue</th>
                        <th>Average Payment</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($policy_performance as $policy): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($policy['type_name']); ?></td>
                        <td><?php echo $policy['payment_count']; ?></td>
                        <td>KSH <?php echo number_format($policy['total_revenue'], 2); ?></td>
                        <td>KSH <?php echo number_format($policy['total_revenue'] / $policy['payment_count'], 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Key Insights -->
        <div class="section">
            <div class="section-title">Key Insights</div>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
                <ul style="list-style-type: disc; padding-left: 20px; line-height: 1.8;">
                    <li>Total revenue for the period: <strong>KSH <?php echo number_format($totals['total_revenue'], 2); ?></strong></li>
                    <li>Most popular payment method: <strong><?php echo !empty($payment_methods) ? $payment_methods[0]['payment_method'] : 'N/A'; ?></strong></li>
                    <li>Average payment amount: <strong>KSH <?php echo number_format($totals['total_revenue'] / max($totals['total_payments'], 1), 2); ?></strong></li>
                    <?php if(!empty($policy_performance)): ?>
                    <li>Top performing policy type: <strong><?php echo $policy_performance[0]['type_name']; ?></strong></li>
                    <?php endif; ?>
                    <li>Pending payments amount: <strong>KSH <?php echo number_format($totals['pending_amount'], 2); ?></strong></li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Zamara Insurance Limited - Financial Report</strong></p>
            <p>Generated on: <?php echo date('M d, Y H:i:s'); ?></p>
            <p>Report prepared by: <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></p>
            <p>This report is confidential and intended for internal use only.</p>
        </div>

        <!-- Print Button -->
        <button class="print-btn" onclick="window.print()">Print Report</button>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
