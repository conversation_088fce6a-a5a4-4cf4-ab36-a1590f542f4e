<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$page_title = "Audit Log";
$page_description = "View system audit trail and financial transaction logs.";

// Get audit trail data (simulated from various tables)
try {
    // Payment activities
    $payment_activities = [];
    $payment_query = "SELECT 'Payment' as activity_type, p.payment_number as reference,
                             CONCAT('Payment of KSH ', FORMAT(p.amount, 2), ' recorded for policy ', cp.policy_number) as description,
                             CONCAT(recorder.first_name, ' ', recorder.last_name) as performed_by,
                             p.created_at as activity_date
                      FROM payments p
                      JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
                      LEFT JOIN users recorder ON p.recorded_by = recorder.user_id
                      ORDER BY p.created_at DESC
                      LIMIT 20";
    
    $stmt = $conn->prepare($payment_query);
    $stmt->execute();
    $payment_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Invoice activities
    $invoice_activities = [];
    $invoice_query = "SELECT 'Invoice' as activity_type, i.invoice_number as reference,
                             CONCAT('Invoice for KSH ', FORMAT(i.amount, 2), ' generated for policy ', cp.policy_number) as description,
                             CONCAT(creator.first_name, ' ', creator.last_name) as performed_by,
                             i.created_at as activity_date
                      FROM invoices i
                      JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id
                      LEFT JOIN users creator ON i.created_by = creator.user_id
                      ORDER BY i.created_at DESC
                      LIMIT 20";
    
    $stmt = $conn->prepare($invoice_query);
    $stmt->execute();
    $invoice_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // User login activities
    $login_activities = [];
    $login_query = "SELECT 'Login' as activity_type, u.username as reference,
                           CONCAT('User logged in from role: ', r.role_name) as description,
                           CONCAT(u.first_name, ' ', u.last_name) as performed_by,
                           u.last_login as activity_date
                    FROM users u
                    JOIN user_roles ur ON u.user_id = ur.user_id
                    JOIN roles r ON ur.role_id = r.role_id
                    WHERE u.last_login IS NOT NULL
                    ORDER BY u.last_login DESC
                    LIMIT 10";
    
    $stmt = $conn->prepare($login_query);
    $stmt->execute();
    $login_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Policy activities
    $policy_activities = [];
    $policy_query = "SELECT 'Policy' as activity_type, cp.policy_number as reference,
                            CONCAT('Policy assigned to customer ', CONCAT(u.first_name, ' ', u.last_name)) as description,
                            CONCAT(agent.first_name, ' ', agent.last_name) as performed_by,
                            cp.created_at as activity_date
                     FROM customer_policies cp
                     JOIN users u ON cp.customer_id = u.user_id
                     LEFT JOIN users agent ON cp.assigned_by = agent.user_id
                     ORDER BY cp.created_at DESC
                     LIMIT 15";
    
    $stmt = $conn->prepare($policy_query);
    $stmt->execute();
    $policy_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine all activities
    $all_activities = array_merge($payment_activities, $invoice_activities, $login_activities, $policy_activities);
    
    // Sort by date
    usort($all_activities, function($a, $b) {
        return strtotime($b['activity_date']) - strtotime($a['activity_date']);
    });

    // Limit to 50 most recent activities
    $all_activities = array_slice($all_activities, 0, 50);

} catch(PDOException $e) {
    $error_message = "Error fetching audit log: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #6c757d; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .back-link { display: inline-block; margin-bottom: 20px; color: #6c757d; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .filters { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end; }
        .form-group { }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background-color: #545b62; }
        .audit-timeline { position: relative; }
        .timeline-item { display: flex; margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #6c757d; }
        .timeline-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold; color: white; }
        .icon-payment { background-color: #28a745; }
        .icon-invoice { background-color: #6f42c1; }
        .icon-login { background-color: #17a2b8; }
        .icon-policy { background-color: #ffc107; color: #212529; }
        .timeline-content { flex: 1; }
        .activity-header { display: flex; justify-content: space-between; align-items: start; margin-bottom: 5px; }
        .activity-type { font-weight: 600; color: #6c757d; font-size: 12px; }
        .activity-time { font-size: 12px; color: #6c757d; }
        .activity-description { margin-bottom: 5px; }
        .activity-reference { font-size: 12px; color: #6c757d; }
        .activity-user { font-size: 12px; color: #6c757d; font-style: italic; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #6c757d; }
        .stat-number { font-size: 24px; font-weight: bold; color: #6c757d; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .export-btn { background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px; }
        .export-btn:hover { background-color: #218838; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="settings.php" class="back-link">← Back to Settings</a>
        
        <div class="content">
            <!-- Audit Statistics -->
            <div class="stats-grid">
                <?php
                $payment_count = count($payment_activities);
                $invoice_count = count($invoice_activities);
                $login_count = count($login_activities);
                $policy_count = count($policy_activities);
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $payment_count; ?></div>
                    <div class="stat-label">Payment Activities</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $invoice_count; ?></div>
                    <div class="stat-label">Invoice Activities</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $login_count; ?></div>
                    <div class="stat-label">Login Activities</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $policy_count; ?></div>
                    <div class="stat-label">Policy Activities</div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters">
                <h4 style="margin-bottom: 15px;">Filter Audit Log</h4>
                <div class="filter-row">
                    <div class="form-group">
                        <label>Activity Type</label>
                        <select id="activityFilter" onchange="filterActivities()">
                            <option value="">All Activities</option>
                            <option value="Payment">Payments</option>
                            <option value="Invoice">Invoices</option>
                            <option value="Login">Logins</option>
                            <option value="Policy">Policies</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Date From</label>
                        <input type="date" id="dateFrom" onchange="filterActivities()">
                    </div>
                    <div class="form-group">
                        <label>Date To</label>
                        <input type="date" id="dateTo" onchange="filterActivities()">
                    </div>
                    <div class="form-group">
                        <label>Search</label>
                        <input type="text" id="searchInput" placeholder="Search activities..." onkeyup="filterActivities()">
                    </div>
                    <div class="form-group">
                        <button class="btn" onclick="clearFilters()">Clear Filters</button>
                        <button class="export-btn" onclick="exportAuditLog()">Export Log</button>
                    </div>
                </div>
            </div>

            <!-- Audit Timeline -->
            <div class="audit-timeline">
                <h3 style="margin-bottom: 20px;">Recent Activities (<?php echo count($all_activities); ?>)</h3>
                
                <?php if(!empty($all_activities)): ?>
                    <?php foreach($all_activities as $activity): ?>
                    <div class="timeline-item" data-type="<?php echo $activity['activity_type']; ?>" data-date="<?php echo $activity['activity_date']; ?>">
                        <div class="timeline-icon icon-<?php echo strtolower($activity['activity_type']); ?>">
                            <?php
                            switch($activity['activity_type']) {
                                case 'Payment': echo 'P'; break;
                                case 'Invoice': echo 'I'; break;
                                case 'Login': echo 'L'; break;
                                case 'Policy': echo 'O'; break;
                                default: echo '?'; break;
                            }
                            ?>
                        </div>
                        <div class="timeline-content">
                            <div class="activity-header">
                                <span class="activity-type"><?php echo strtoupper($activity['activity_type']); ?></span>
                                <span class="activity-time"><?php echo date('M d, Y H:i', strtotime($activity['activity_date'])); ?></span>
                            </div>
                            <div class="activity-description"><?php echo htmlspecialchars($activity['description']); ?></div>
                            <div class="activity-reference">Reference: <?php echo htmlspecialchars($activity['reference']); ?></div>
                            <div class="activity-user">Performed by: <?php echo htmlspecialchars($activity['performed_by'] ?: 'System'); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <h3>No audit activities found</h3>
                        <p>Audit trail will appear here as system activities occur.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function filterActivities() {
            const typeFilter = document.getElementById('activityFilter').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            const items = document.querySelectorAll('.timeline-item');
            
            items.forEach(item => {
                let show = true;
                
                // Filter by type
                if (typeFilter && item.dataset.type !== typeFilter) {
                    show = false;
                }
                
                // Filter by date range
                const itemDate = new Date(item.dataset.date).toISOString().split('T')[0];
                if (dateFrom && itemDate < dateFrom) {
                    show = false;
                }
                if (dateTo && itemDate > dateTo) {
                    show = false;
                }
                
                // Filter by search term
                if (searchTerm && !item.textContent.toLowerCase().includes(searchTerm)) {
                    show = false;
                }
                
                item.style.display = show ? 'flex' : 'none';
            });
        }

        function clearFilters() {
            document.getElementById('activityFilter').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('searchInput').value = '';
            filterActivities();
        }

        function exportAuditLog() {
            const typeFilter = document.getElementById('activityFilter').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            
            let url = 'export_audit_log.php?';
            if (typeFilter) url += 'type=' + typeFilter + '&';
            if (dateFrom) url += 'date_from=' + dateFrom + '&';
            if (dateTo) url += 'date_to=' + dateTo + '&';
            
            window.open(url, '_blank');
        }

        // Set default date range to last 30 days
        document.getElementById('dateTo').value = new Date().toISOString().split('T')[0];
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
    </script>
</body>
</html>
