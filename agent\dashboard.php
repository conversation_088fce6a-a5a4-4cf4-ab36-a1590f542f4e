<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_agent = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Insurance Agent") {
        $is_agent = true;
        break;
    }
}

if(!$is_agent) {
    header("location: ../dashboard.php");
    exit;
}

$page_title = "Agent Dashboard";
$page_description = "Manage insurance policies and customers.";

require_once '../includes/db_config.php';


$query = "SELECT COUNT(*) as total_policies FROM policies";
$stmt = $conn->prepare($query);
$stmt->execute();
$policy_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_policies'];


$query = "SELECT COUNT(DISTINCT u.user_id) as total_customers
          FROM users u
          JOIN user_roles ur ON u.user_id = ur.user_id
          JOIN roles r ON ur.role_id = r.role_id
          WHERE r.role_name = 'Customer'";
$stmt = $conn->prepare($query);
$stmt->execute();
$customer_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_customers'];


$query = "SELECT COUNT(*) as total_claims FROM claims";
$stmt = $conn->prepare($query);
$stmt->execute();
$claim_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_claims'];


$query = "SELECT p.policy_id, p.policy_name, pt.type_name, p.premium_amount, p.created_at
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          ORDER BY p.created_at DESC
          LIMIT 5";
$stmt = $conn->prepare($query);
$stmt->execute();
$recent_policies = $stmt->fetchAll(PDO::FETCH_ASSOC);


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Zamara' : 'Zamara - Insurance & Financial Services'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Zamara offers comprehensive insurance and financial services to individuals and businesses.'; ?>">

    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .agent-header {
            background-color: #28a745;
            color: white;
        }

        .agent-sidebar {
            background-color: #f8f9fa;
            min-height: calc(100vh - 70px);
            border-right: 1px solid #ddd;
        }

        .agent-sidebar a {
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }

        .agent-sidebar a:hover, .agent-sidebar a.active {
            background-color: #28a745;
            color: white;
        }

        .agent-content {
            padding: 20px;
        }

        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .stat-card p {
            font-size: 24px;
            font-weight: 700;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
    </style>
</head>
<body>

    <header class="agent-header">
        <div class="container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
            <div>
                <h2>Zamara Insurance Agent</h2>
            </div>

            <div style="display: flex; align-items: center;">
                <span style="margin-right: 15px;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                <a href="../logout.php" style="color: white; text-decoration: none; background-color: rgba(0,0,0,0.2); padding: 5px 10px; border-radius: 4px;">Logout</a>
            </div>
        </div>
    </header>

    <div style="display: flex;">

        <div class="agent-sidebar" style="width: 250px; padding: 20px;">
            <h3 style="margin-bottom: 20px;">Agent Menu</h3>

            <nav>
                <a href="dashboard.php" class="active">Dashboard</a>
                <a href="policies.php">Manage Policies</a>
                <a href="create_policy.php">Create New Policy</a>
                <a href="customers.php">Manage Customers</a>
                <a href="claims.php">Manage Claims</a>
                <a href="../index.php">View Website</a>
            </nav>
        </div>


        <div class="agent-content" style="flex: 1;">
            <h1 style="margin-bottom: 20px;">Agent Dashboard</h1>


            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="border-top: 4px solid #28a745;">
                    <h3>Total Policies</h3>
                    <p style="color: #28a745;"><?php echo $policy_count; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #0056b3;">
                    <h3>Total Customers</h3>
                    <p style="color: #0056b3;"><?php echo $customer_count; ?></p>
                </div>

                <div class="stat-card" style="border-top: 4px solid #ffc107;">
                    <h3>Total Claims</h3>
                    <p style="color: #ffc107;"><?php echo $claim_count; ?></p>
                </div>
            </div>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 30px;">
                <h2 style="margin-bottom: 20px;">Recent Policies</h2>

                <?php if(count($recent_policies) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Policy Name</th>
                                <th>Type</th>
                                <th>Premium</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($recent_policies as $policy): ?>
                                <tr>
                                    <td><?php echo $policy['policy_id']; ?></td>
                                    <td><?php echo htmlspecialchars($policy['policy_name']); ?></td>
                                    <td><?php echo htmlspecialchars($policy['type_name']); ?></td>
                                    <td>$<?php echo number_format($policy['premium_amount'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($policy['created_at'])); ?></td>
                                    <td>
                                        <a href="view_policy.php?id=<?php echo $policy['policy_id']; ?>" style="color: #0056b3; margin-right: 10px;">View</a>
                                        <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" style="color: #28a745; margin-right: 10px;">Edit</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>No policies found.</p>
                <?php endif; ?>

                <div style="margin-top: 20px;">
                    <a href="policies.php" style="display: inline-block; background-color: #28a745; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Policies</a>
                    <a href="create_policy.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none; margin-left: 10px;">Create New Policy</a>
                </div>
            </div>


            <div style="background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h2 style="margin-bottom: 20px;">Quick Actions</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <a href="create_policy.php" style="background-color: #28a745; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Create New Policy</h3>
                    </a>

                    <a href="customers.php" style="background-color: #0056b3; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Manage Customers</h3>
                    </a>

                    <a href="claims.php" style="background-color: #ffc107; color: white; padding: 15px; border-radius: 8px; text-align: center; text-decoration: none;">
                        <h3>Review Claims</h3>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <footer style="background-color: #f8f9fa; color: #333; padding: 20px 0; text-align: center; margin-top: 30px; border-top: 1px solid #ddd;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Zamara Insurance. All Rights Reserved.</p>
        </div>
    </footer>


    <script src="../assets/js/main.js"></script>
</body>
</html>
