


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

a {
    text-decoration: none;
    color: #0056b3;
}

a:hover {
    color: #003d7a;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}


header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: #0056b3;
}

.logo img {
    max-height: 50px;
}


nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: #333;
    font-weight: 500;
    padding: 10px 5px;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #0056b3;
}

.active {
    color: #0056b3;
    border-bottom: 2px solid #0056b3;
}


.mobile-menu-btn {
    display: none;
    cursor: pointer;
}


.hero {
    background-color: #0056b3;
    color: #fff;
    padding: 100px 0;
    text-align: center;
}

.hero h1 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero p {
    font-size: 20px;
    max-width: 800px;
    margin: 0 auto 30px;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    background-color: #fff;
    color: #0056b3;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}


section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
}

.section-title h2 {
    font-size: 36px;
    color: #0056b3;
    margin-bottom: 15px;
}

.section-title p {
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}


.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.service-content {
    padding: 20px;
}

.service-content h3 {
    margin-bottom: 10px;
    color: #0056b3;
}


footer {
    background-color: #333;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    margin-bottom: 20px;
    font-size: 18px;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #ccc;
    transition: color 0.3s;
}

.footer-column ul li a:hover {
    color: #fff;
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #ccc;
}


@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    nav ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #fff;
        flex-direction: column;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    nav ul.show {
        display: flex;
    }

    nav ul li {
        margin: 0;
    }

    nav ul li a {
        display: block;
        padding: 15px;
        border-bottom: 1px solid #eee;
    }

    .hero h1 {
        font-size: 36px;
    }

    .hero p {
        font-size: 18px;
    }
}
