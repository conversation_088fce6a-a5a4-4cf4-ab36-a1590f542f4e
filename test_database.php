<?php
// Database connection test
echo "<h2>Database Connection Test</h2>";

$db_name = "zamara_db";
$db_user = "root";
$db_password = "";

$hosts_to_try = ["127.0.0.1", "localhost", "::1"];
$conn = null;
$last_error = "";

foreach ($hosts_to_try as $db_host) {
    try {
        $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ Connected to database successfully using host: $db_host</p>";
        break;
    } catch(PDOException $e) {
        $last_error = $e->getMessage();
        echo "<p style='color: red;'>❌ Failed to connect using host: $db_host - Error: " . $e->getMessage() . "</p>";
        continue;
    }
}

if ($conn === null) {
    die("<p style='color: red;'>❌ Could not connect to the database. Last error: " . $last_error . "</p>");
}

// Test if zamara_db database exists
try {
    $stmt = $conn->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p style='color: green;'>✅ Current database: " . $result['current_db'] . "</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking current database: " . $e->getMessage() . "</p>";
}

// Test if users table exists
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Users table exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Users table does not exist</p>";
    }
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking users table: " . $e->getMessage() . "</p>";
}

// Check users in the table
try {
    $stmt = $conn->query("SELECT user_id, username, first_name, last_name, status FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<h3 style='color: green;'>✅ Found " . count($users) . " users:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>First Name</th><th>Last Name</th><th>Status</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['first_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No users found in the users table</p>";
    }
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error fetching users: " . $e->getMessage() . "</p>";
}

// Test password verification for admin user
try {
    $stmt = $conn->prepare("SELECT user_id, username, password FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<h3>🔐 Admin Password Test:</h3>";
        echo "<p>Admin user found with ID: " . $admin['user_id'] . "</p>";
        echo "<p>Stored password hash: " . substr($admin['password'], 0, 20) . "...</p>";
        
        // Test password verification
        $test_password = "admin123";
        if (password_verify($test_password, $admin['password'])) {
            echo "<p style='color: green;'>✅ Password 'admin123' verification: SUCCESS</p>";
        } else {
            echo "<p style='color: red;'>❌ Password 'admin123' verification: FAILED</p>";
            echo "<p>This means the stored password hash doesn't match 'admin123'</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin user not found</p>";
    }
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error testing admin password: " . $e->getMessage() . "</p>";
}

// Check user roles
try {
    $stmt = $conn->query("SELECT COUNT(*) as role_count FROM user_roles");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p style='color: green;'>✅ User roles assigned: " . $result['role_count'] . "</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking user roles: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>📋 Summary:</h3>";
echo "<p>If you see any red ❌ errors above, that's the problem!</p>";
echo "<p>If everything is green ✅, then the database is working correctly.</p>";
?>
