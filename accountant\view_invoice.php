<?php
session_start();

// Check if user is logged in and is accountant
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "System Accountant") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($invoice_id <= 0) {
    header("location: invoices.php");
    exit;
}

// Get invoice details
$query = "SELECT i.*, cp.policy_number, p.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email, customer.phone as customer_phone,
                 customer.address as customer_address,
                 CONCAT(creator.first_name, ' ', creator.last_name) as created_by_name
          FROM invoices i
          JOIN customer_policies cp ON i.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          LEFT JOIN users creator ON i.created_by = creator.user_id
          WHERE i.invoice_id = :invoice_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':invoice_id', $invoice_id);
$stmt->execute();
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$invoice) {
    header("location: invoices.php");
    exit;
}

$page_title = "Invoice Details - " . $invoice['invoice_number'];
$page_description = "View detailed invoice information.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #6f42c1; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .invoice-header { display: grid; grid-template-columns: 1fr auto; gap: 20px; align-items: start; margin-bottom: 30px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 14px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .status-badge { padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-paid { background-color: #d4edda; color: #155724; }
        .status-overdue { background-color: #f8d7da; color: #721c24; }
        .status-cancelled { background-color: #e2e3e5; color: #383d41; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #6f42c1; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; }
        .info-section h4 { color: #6f42c1; margin-bottom: 15px; border-bottom: 2px solid #6f42c1; padding-bottom: 5px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: 600; color: #6c757d; font-size: 12px; margin-bottom: 3px; }
        .info-value { font-size: 14px; }
        .amount-display { font-size: 28px; font-weight: bold; color: #6f42c1; text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 8px; margin: 20px 0; }
        .due-date { text-align: center; margin: 10px 0; }
        .overdue { color: #dc3545; font-weight: bold; }
        .upcoming { color: #ffc107; font-weight: bold; }
        .timeline { border-left: 3px solid #6f42c1; padding-left: 20px; margin-left: 10px; }
        .timeline-item { margin-bottom: 20px; position: relative; }
        .timeline-item::before { content: ''; position: absolute; left: -26px; top: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: #6f42c1; }
        .timeline-date { font-size: 12px; color: #6c757d; }
        .timeline-content { margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="invoices.php" class="back-link">← Back to Invoices</a>
        
        <!-- Invoice Header -->
        <div class="content">
            <div class="invoice-header">
                <div>
                    <h2>Invoice #<?php echo htmlspecialchars($invoice['invoice_number']); ?></h2>
                    <p style="color: #6c757d; margin-bottom: 10px;">
                        Invoice ID: <?php echo $invoice['invoice_id']; ?> | 
                        Created: <?php echo date('M d, Y', strtotime($invoice['created_at'])); ?>
                    </p>
                    <span class="status-badge status-<?php echo strtolower($invoice['status']); ?>">
                        <?php echo $invoice['status']; ?>
                    </span>
                </div>
                <div style="display: flex; gap: 10px; flex-direction: column;">
                    <button onclick="printInvoice()" class="btn btn-primary">Print Invoice</button>
                    <a href="print_invoice.php?id=<?php echo $invoice['invoice_id']; ?>" target="_blank" class="btn btn-info">Download PDF</a>
                    <?php if($invoice['status'] == 'Pending'): ?>
                        <button onclick="markAsPaid()" class="btn btn-success">Mark as Paid</button>
                        <button onclick="sendReminder()" class="btn btn-warning">Send Reminder</button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Invoice Amount -->
            <div class="amount-display">
                KSH <?php echo number_format($invoice['amount'], 2); ?>
            </div>

            <!-- Due Date Status -->
            <div class="due-date">
                <?php
                $due_date = strtotime($invoice['due_date']);
                $today = strtotime(date('Y-m-d'));
                $days_diff = ($due_date - $today) / (60 * 60 * 24);
                
                if($invoice['status'] == 'Pending') {
                    if($days_diff < 0) {
                        echo '<span class="overdue">OVERDUE by ' . abs(floor($days_diff)) . ' days</span>';
                    } elseif($days_diff <= 7) {
                        echo '<span class="upcoming">Due in ' . floor($days_diff) . ' days</span>';
                    } else {
                        echo 'Due on ' . date('M d, Y', $due_date);
                    }
                } else {
                    echo 'Due date was ' . date('M d, Y', $due_date);
                }
                ?>
            </div>
        </div>

        <!-- Invoice Details -->
        <div class="info-grid">
            <!-- Customer Information -->
            <div class="info-section">
                <h4>Bill To</h4>
                <div class="info-item">
                    <div class="info-label">CUSTOMER NAME</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">EMAIL</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['customer_email']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">PHONE</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['customer_phone'] ?: 'Not provided'); ?></div>
                </div>
                <?php if($invoice['customer_address']): ?>
                <div class="info-item">
                    <div class="info-label">ADDRESS</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['customer_address']); ?></div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Policy Information -->
            <div class="info-section">
                <h4>Policy Information</h4>
                <div class="info-item">
                    <div class="info-label">POLICY NUMBER</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['policy_number']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">POLICY NAME</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['policy_name']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">POLICY TYPE</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['type_name']); ?></div>
                </div>
            </div>

            <!-- Invoice Information -->
            <div class="info-section">
                <h4>Invoice Information</h4>
                <div class="info-item">
                    <div class="info-label">INVOICE DATE</div>
                    <div class="info-value"><?php echo date('M d, Y', strtotime($invoice['created_at'])); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">DUE DATE</div>
                    <div class="info-value"><?php echo date('M d, Y', strtotime($invoice['due_date'])); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">CREATED BY</div>
                    <div class="info-value"><?php echo htmlspecialchars($invoice['created_by_name'] ?: 'System'); ?></div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="info-section">
                <h4>Payment Information</h4>
                <div class="info-item">
                    <div class="info-label">AMOUNT DUE</div>
                    <div class="info-value"><strong>KSH <?php echo number_format($invoice['amount'], 2); ?></strong></div>
                </div>
                <div class="info-item">
                    <div class="info-label">STATUS</div>
                    <div class="info-value">
                        <span class="status-badge status-<?php echo strtolower($invoice['status']); ?>">
                            <?php echo $invoice['status']; ?>
                        </span>
                    </div>
                </div>
                <?php if($invoice['paid_at']): ?>
                <div class="info-item">
                    <div class="info-label">PAID ON</div>
                    <div class="info-value"><?php echo date('M d, Y H:i', strtotime($invoice['paid_at'])); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Invoice Description -->
        <?php if($invoice['description']): ?>
        <div class="content">
            <h4>Description</h4>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px;">
                <?php echo nl2br(htmlspecialchars($invoice['description'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Invoice Timeline -->
        <div class="content">
            <h4>Invoice Timeline</h4>
            <div class="timeline" style="margin-top: 20px;">
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y H:i:s', strtotime($invoice['created_at'])); ?></div>
                    <div class="timeline-content">
                        <strong>Invoice Created</strong><br>
                        Invoice generated by <?php echo htmlspecialchars($invoice['created_by_name'] ?: 'System'); ?>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y', strtotime($invoice['due_date'])); ?></div>
                    <div class="timeline-content">
                        <strong>Due Date</strong><br>
                        Payment due date
                    </div>
                </div>
                
                <?php if($invoice['paid_at']): ?>
                <div class="timeline-item">
                    <div class="timeline-date"><?php echo date('M d, Y H:i:s', strtotime($invoice['paid_at'])); ?></div>
                    <div class="timeline-content">
                        <strong>Payment Received</strong><br>
                        Invoice marked as paid
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="content">
            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <a href="invoices.php" class="btn btn-secondary">Back to Invoices</a>
                <button onclick="printInvoice()" class="btn btn-primary">Print Invoice</button>
                <a href="print_invoice.php?id=<?php echo $invoice['invoice_id']; ?>" target="_blank" class="btn btn-info">Download PDF</a>
                <?php if($invoice['status'] == 'Pending'): ?>
                    <button onclick="markAsPaid()" class="btn btn-success">Mark as Paid</button>
                    <button onclick="sendReminder()" class="btn btn-warning">Send Reminder</button>
                    <button onclick="cancelInvoice()" class="btn btn-danger">Cancel Invoice</button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function printInvoice() {
            window.print();
        }

        function markAsPaid() {
            if(confirm('Mark this invoice as paid?')) {
                window.location.href = 'mark_paid.php?invoice_id=<?php echo $invoice['invoice_id']; ?>';
            }
        }

        function sendReminder() {
            if(confirm('Send payment reminder to customer?')) {
                alert('Payment reminder sent successfully!');
                // In a real system, this would send an email
            }
        }

        function cancelInvoice() {
            const reason = prompt('Enter cancellation reason:');
            if(reason) {
                if(confirm('Cancel this invoice?')) {
                    window.location.href = 'cancel_invoice.php?invoice_id=<?php echo $invoice['invoice_id']; ?>&reason=' + encodeURIComponent(reason);
                }
            }
        }

        // Print styles
        window.addEventListener('beforeprint', function() {
            document.querySelector('.header').style.display = 'none';
            document.querySelector('.back-link').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.style.display = 'none');
        });

        window.addEventListener('afterprint', function() {
            document.querySelector('.header').style.display = 'block';
            document.querySelector('.back-link').style.display = 'inline-block';
            document.querySelectorAll('.btn').forEach(btn => btn.style.display = 'inline-block');
        });
    </script>
</body>
</html>
