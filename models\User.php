<?php
class User {
    private $conn;
    private $table_name = "users";


    public $user_id;
    public $username;
    public $email;
    public $password;
    public $first_name;
    public $last_name;
    public $phone;
    public $address;
    public $city;
    public $country;
    public $date_of_birth;
    public $profile_image;
    public $is_active;
    public $last_login;
    public $created_at;
    public $updated_at;


    public function __construct($db) {
        $this->conn = $db;
    }


    public function create() {

        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->password = htmlspecialchars(strip_tags($this->password));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));


        $password_hash = password_hash($this->password, PASSWORD_BCRYPT);


        $query = "INSERT INTO " . $this->table_name . "
                SET username = :username,
                    email = :email,
                    password = :password,
                    first_name = :first_name,
                    last_name = :last_name,
                    phone = :phone";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password", $password_hash);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":phone", $this->phone);


        if($stmt->execute()) {

            $this->user_id = $this->conn->lastInsertId();

            $this->assignRole(3);

            return true;
        }

        return false;
    }


    public function assignRole($role_id) {

        $query = "INSERT INTO user_roles
                SET user_id = :user_id,
                    role_id = :role_id";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":role_id", $role_id);


        return $stmt->execute();
    }


    public function login() {

        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->password = htmlspecialchars(strip_tags($this->password));


        $query = "SELECT user_id, username, email, password, first_name, last_name, is_active
                FROM " . $this->table_name . "
                WHERE username = :username
                LIMIT 0,1";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":username", $this->username);


        $stmt->execute();


        if($stmt->rowCount() > 0) {

            $row = $stmt->fetch(PDO::FETCH_ASSOC);


            if($row['is_active'] == 0) {
                return false;
            }


            if(password_verify($this->password, $row['password'])) {

                $this->user_id = $row['user_id'];
                $this->email = $row['email'];
                $this->first_name = $row['first_name'];
                $this->last_name = $row['last_name'];


                $this->updateLastLogin();

                return true;
            }
        }

        return false;
    }


    private function updateLastLogin() {

        $query = "UPDATE " . $this->table_name . "
                SET last_login = NOW()
                WHERE user_id = :user_id";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);


        $stmt->execute();
    }


    public function getRoles() {

        $query = "SELECT r.role_id, r.role_name
                FROM roles r
                JOIN user_roles ur ON r.role_id = ur.role_id
                WHERE ur.user_id = :user_id";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);


        $stmt->execute();

        return $stmt;
    }


    public function usernameExists() {

        $this->username = htmlspecialchars(strip_tags($this->username));


        $query = "SELECT user_id
                FROM " . $this->table_name . "
                WHERE username = :username
                LIMIT 0,1";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":username", $this->username);


        $stmt->execute();

        return $stmt->rowCount() > 0;
    }


    public function emailExists() {

        $this->email = htmlspecialchars(strip_tags($this->email));


        $query = "SELECT user_id
                FROM " . $this->table_name . "
                WHERE email = :email
                LIMIT 0,1";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":email", $this->email);


        $stmt->execute();

        return $stmt->rowCount() > 0;
    }


    public function readOne() {

        $query = "SELECT * FROM " . $this->table_name . " WHERE user_id = :user_id LIMIT 0,1";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);


        $stmt->execute();


        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {

            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->first_name = $row['first_name'];
            $this->last_name = $row['last_name'];
            $this->phone = $row['phone'];
            $this->address = $row['address'];
            $this->city = $row['city'];
            $this->country = $row['country'];
            $this->date_of_birth = $row['date_of_birth'];
            $this->profile_image = $row['profile_image'];
            $this->is_active = $row['is_active'];
            $this->last_login = $row['last_login'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }


    public function update() {

        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->city = htmlspecialchars(strip_tags($this->city));
        $this->country = htmlspecialchars(strip_tags($this->country));
        $this->date_of_birth = htmlspecialchars(strip_tags($this->date_of_birth));


        $query = "UPDATE " . $this->table_name . "
                SET first_name = :first_name,
                    last_name = :last_name,
                    phone = :phone,
                    address = :address,
                    city = :city,
                    country = :country,
                    date_of_birth = :date_of_birth
                WHERE user_id = :user_id";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":phone", $this->phone);
        $stmt->bindParam(":address", $this->address);
        $stmt->bindParam(":city", $this->city);
        $stmt->bindParam(":country", $this->country);
        $stmt->bindParam(":date_of_birth", $this->date_of_birth);


        if($stmt->execute()) {
            return true;
        }

        return false;
    }


    public function changePassword($new_password) {

        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $new_password = htmlspecialchars(strip_tags($new_password));


        $password_hash = password_hash($new_password, PASSWORD_BCRYPT);


        $query = "UPDATE " . $this->table_name . "
                SET password = :password
                WHERE user_id = :user_id";


        $stmt = $this->conn->prepare($query);


        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":password", $password_hash);


        if($stmt->execute()) {
            return true;
        }

        return false;
    }
}
?>
