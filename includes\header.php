<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . " - Zamara Insurance" : "Zamara Insurance"; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : "Zamara Insurance - Protecting what matters most"; ?>">
    <style>

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.7;
            color: #333;
            background-color: #fafbff;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }


        header {
            background-color: #fff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 15px 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
        }

        .logo {
            font-size: 28px;
            font-weight: 800;
            color: #0d0b8a;
            letter-spacing: -0.5px;
            transition: transform 0.3s ease;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
        }

        .logo a:hover {
            transform: scale(1.05);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 10px;
        }

        nav ul li {
            margin-left: 15px;
            position: relative;
        }

        nav ul li a {
            color: #333;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        nav ul li a:hover {
            color: #0d0b8a;
            background-color: rgba(13, 11, 138, 0.08);
            transform: translateY(-2px);
        }

        .btn-primary {
            background-color: #0d0b8a;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 30px;
            box-shadow: 0 4px 15px rgba(13, 11, 138, 0.2);
            transition: all 0.3s ease;
            border: 2px solid #0d0b8a;
            font-weight: 600;
        }

        .btn-primary:hover {
            background-color: #fff !important;
            color: #0d0b8a !important;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(13, 11, 138, 0.25);
        }


        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border-radius: 12px;
            padding: 15px 0;
            min-width: 220px;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .dropdown-menu:before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 16px;
            background-color: white;
            transform: rotate(45deg);
            border-radius: 4px;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            top: calc(100% + 5px);
        }

        .dropdown-menu li {
            margin: 0;
            padding: 0 5px;
        }

        .dropdown-menu li a {
            display: block;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.2s ease;
        }

        .dropdown-menu li a:hover {
            background-color: rgba(16, 14, 171, 0.05);
            transform: translateX(5px);
        }


        section {
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        section:nth-child(even) {
            background-color: #f8f9ff;
        }

        h1 {
            color: #0d0b8a;
            margin-bottom: 25px;
            font-weight: 800;
            font-size: 2.8rem;
            line-height: 1.2;
            letter-spacing: -0.5px;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
        }

        h2 {
            color: #0d0b8a;
            margin-bottom: 20px;
            font-weight: 700;
            font-size: 2.2rem;
            line-height: 1.3;
            position: relative;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 60px;
            height: 4px;
            background-color: #0d0b8a;
            border-radius: 2px;
        }

        .section-title h2:after {
            left: 50%;
            transform: translateX(-50%);
        }

        h3 {
            color: #0d0b8a;
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 1.5rem;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
        }

        p {
            margin-bottom: 20px;
            font-size: 1.05rem;
            color: #333;
            line-height: 1.8;
        }


        .card {
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.03);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(16, 14, 171, 0.1);
        }


        .btn {
            display: inline-block;
            padding: 12px 28px;
            background-color: #0d0b8a;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 5px 15px rgba(13, 11, 138, 0.2);
            transition: all 0.3s ease;
            border: 2px solid #0d0b8a;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0%;
            height: 100%;
            background-color: white;
            transition: all 0.3s ease;
            z-index: -1;
        }

        .btn:hover {
            color: #0d0b8a;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(13, 11, 138, 0.25);
        }

        .btn:hover:before {
            width: 100%;
        }


        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .service-card {
            background-color: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.03);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(16, 14, 171, 0.1);
        }

        .service-card img {
            width: 100%;
            height: 220px;
            object-fit: cover;
            transition: all 0.5s ease;
        }

        .service-card:hover img {
            transform: scale(1.05);
        }

        .service-content {
            padding: 25px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .service-content h3 {
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .service-content p {
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .service-content a {
            align-self: flex-start;
            padding: 10px 20px;
            background-color: rgba(16, 14, 171, 0.05);
            color: #100eab;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .service-content a:hover {
            background-color: #100eab;
            color: white;
            transform: translateX(5px);
        }


        footer {
            background-color: #0a0970;
            color: white;
            padding: 70px 0 30px;
            margin-top: 80px;
            position: relative;
        }

        footer:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('assets/images/pattern.png');
            opacity: 0.05;
            background-size: cover;
        }

        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .footer-section {
            flex: 1;
            margin-right: 40px;
            margin-bottom: 40px;
            min-width: 250px;
        }

        .footer-section h3 {
            color: white;
            margin-bottom: 25px;
            font-size: 1.4rem;
            position: relative;
            padding-bottom: 15px;
        }

        .footer-section h3:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: #fff;
            border-radius: 2px;
        }

        .footer-section p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            display: block;
            padding: 5px 0;
        }

        .footer-section ul li a:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact span {
            display: block;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.8);
        }

        .socials a {
            display: inline-block;
            margin-right: 15px;
            color: white;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .socials a:hover {
            transform: translateY(-5px);
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            position: relative;
            z-index: 1;
        }


        @media screen and (max-width: 992px) {
            h1 {
                font-size: 2.4rem;
            }

            h2 {
                font-size: 2rem;
            }

            section {
                padding: 60px 0;
            }
        }

        @media screen and (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            nav ul {
                margin-top: 20px;
                flex-wrap: wrap;
                justify-content: center;
                gap: 5px;
            }

            nav ul li {
                margin: 5px;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.8rem;
            }

            h3 {
                font-size: 1.3rem;
            }

            .btn {
                padding: 10px 20px;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .footer-section {
                flex: 100%;
                margin-right: 0;
            }
        }

        @media screen and (max-width: 480px) {
            h1 {
                font-size: 1.8rem;
            }

            h2 {
                font-size: 1.6rem;
            }

            section {
                padding: 40px 0;
            }

            .btn {
                padding: 8px 18px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="index.php" style="text-decoration: none; color: #0d0b8a; text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);">
                        Zamara Insurance
                    </a>
                </div>
                <nav>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="browse_policies.php">Policies</a></li>
                        <li><a href="services.php">Services</a></li>
                        <li><a href="#footer" onclick="scrollToFooter(event)">Contact Us</a></li>
                        <?php if(isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true): ?>
                            <li class="dropdown">
                                <a href="#">My Account</a>
                                <ul class="dropdown-menu">
                                    <li><a href="dashboard.php">Dashboard</a></li>
                                    <li><a href="my-policies.php">My Policies</a></li>
                                    <li><a href="file-claim.php">File a Claim</a></li>
                                    <li><a href="payment-history.php">Payment History</a></li>
                                    <li><a href="profile.php">Profile</a></li>
                                    <li><a href="logout.php">Logout</a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li><a href="login.php">Login</a></li>
                            <li><a href="register.php" class="btn-primary">Register</a></li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <main>