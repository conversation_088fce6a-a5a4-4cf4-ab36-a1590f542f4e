<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Set page specific variables
$page_title = "My Claims";
$page_description = "View and track your insurance claims.";

// Include database connection
require_once 'includes/db_config.php';

// Get user's claims
$claims = array();
$query = "SELECT c.*, cp.policy_number, p.policy_name, pt.type_name
          FROM claims c
          JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id
          ORDER BY c.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$claims = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
include_once 'includes/header.php';
?>

<!-- Page Header -->
<section style="background-color: #0056b3; color: white; padding: 60px 0; text-align: center;">
    <div class="container">
        <h1>My Claims</h1>
        <p>View and track your insurance claims.</p>
    </div>
</section>

<!-- Dashboard Section -->
<section id="dashboard">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 30px; padding: 40px 0;">

            <!-- Sidebar -->
            <div>
                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Dashboard Menu</h3>

                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;">
                            <a href="dashboard.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Dashboard Home</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-policies.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Policies</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="file-claim.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">File a Claim</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="my-claims.php" style="display: block; padding: 10px; background-color: #0056b3; color: white; border-radius: 4px; text-decoration: none;">My Claims</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="payment-history.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">Payment History</a>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <a href="profile.php" style="display: block; padding: 10px; background-color: #f8f9fa; color: #333; border-radius: 4px; text-decoration: none; border: 1px solid #ddd;">My Profile</a>
                        </li>
                        <li>
                            <a href="logout.php" style="display: block; padding: 10px; background-color: #dc3545; color: white; border-radius: 4px; text-decoration: none;">Logout</a>
                        </li>
                    </ul>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Need Help?</h3>
                    <p>Contact our customer support:</p>
                    <p><strong>Phone:</strong> +254 123 456 789</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Us</a>
                </div>
            </div>

            <!-- Main Content -->
            <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="margin: 0;">Your Insurance Claims</h2>
                    <a href="file-claim.php" style="background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">File New Claim</a>
                </div>

                <?php if(count($claims) > 0): ?>
                    <!-- Claims List -->
                    <div style="margin-bottom: 30px;">
                        <?php foreach($claims as $claim): ?>
                            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px; border-left: 4px solid
                                <?php
                                    switch($claim['status']) {
                                        case 'Pending': echo '#ffc107'; break;
                                        case 'Under Review': echo '#17a2b8'; break;
                                        case 'Approved': echo '#28a745'; break;
                                        case 'Rejected': echo '#dc3545'; break;
                                        case 'Processed': echo '#20c997'; break;
                                        default: echo '#6c757d';
                                    }
                                ?>;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <h3 style="margin: 0; color: #0056b3;">Claim #<?php echo htmlspecialchars($claim['claim_number']); ?></h3>
                                    <span style="background-color:
                                        <?php
                                            switch($claim['status']) {
                                                case 'Pending': echo '#ffc107'; break;
                                                case 'Under Review': echo '#17a2b8'; break;
                                                case 'Approved': echo '#28a745'; break;
                                                case 'Rejected': echo '#dc3545'; break;
                                                case 'Processed': echo '#20c997'; break;
                                                default: echo '#6c757d';
                                            }
                                        ?>;
                                        color: <?php echo ($claim['status'] == 'Pending') ? '#212529' : 'white'; ?>;
                                        padding: 5px 10px;
                                        border-radius: 4px;
                                        font-size: 14px;">
                                        <?php echo htmlspecialchars($claim['status']); ?>
                                    </span>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
                                    <div>
                                        <p><strong>Policy:</strong> <?php echo htmlspecialchars($claim['policy_number'] . ' - ' . $claim['policy_name']); ?></p>
                                        <p><strong>Type:</strong> <?php echo htmlspecialchars($claim['type_name']); ?></p>
                                        <p><strong>Claim Submitted:</strong> <?php echo date('M d, Y', strtotime($claim['created_at'])); ?></p>
                                        <p><strong>Incident Date:</strong> <?php echo date('M d, Y', strtotime($claim['incident_date'])); ?></p>
                                    </div>
                                    <div>
                                        <p><strong>Claim Amount:</strong> $<?php echo number_format($claim['claim_amount'], 2); ?></p>
                                        <p><strong>Submitted:</strong> <?php echo date('M d, Y', strtotime($claim['created_at'])); ?></p>
                                        <?php if($claim['processed_at']): ?>
                                            <p><strong>Processed:</strong> <?php echo date('M d, Y', strtotime($claim['processed_at'])); ?></p>
                                        <?php endif; ?>
                                        <?php if($claim['status'] == 'Rejected' && !empty($claim['notes'])): ?>
                                            <p><strong>Reason:</strong> <?php echo htmlspecialchars($claim['notes']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div style="margin-bottom: 15px;">
                                    <p><strong>Description:</strong> <?php echo htmlspecialchars($claim['description']); ?></p>
                                </div>

                                <div style="display: flex; gap: 10px;">
                                    <a href="view-claim.php?id=<?php echo $claim['claim_id']; ?>" style="background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none; font-size: 14px;">View Details</a>
                                    <?php if($claim['status'] == 'Pending'): ?>
                                        <a href="edit-claim.php?id=<?php echo $claim['claim_id']; ?>" style="background-color: #ffc107; color: #212529; padding: 8px 15px; border-radius: 4px; text-decoration: none; font-size: 14px;">Edit Claim</a>
                                    <?php endif; ?>
                                    <a href="contact.php?subject=Claim%20Inquiry:%20<?php echo $claim['claim_number']; ?>" style="background-color: #6c757d; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none; font-size: 14px;">Contact Support</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- No Claims Message -->
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; text-align: center;">
                        <p style="margin-bottom: 20px;">You haven't filed any claims yet.</p>
                        <a href="file-claim.php" style="background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none; margin-right: 10px;">File a Claim</a>
                        <a href="my-policies.php" style="background-color: #28a745; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">View My Policies</a>
                    </div>
                <?php endif; ?>

                <!-- Claims Information -->
                <div style="background-color: #f0f5ff; border-radius: 8px; padding: 20px; margin-top: 30px;">
                    <h3 style="margin-bottom: 15px; color: #0056b3;">Understanding Claim Status</h3>
                    <ul style="padding-left: 20px;">
                        <li style="margin-bottom: 10px;"><strong style="color: #ffc107;">Pending:</strong> Your claim has been received and is awaiting initial review.</li>
                        <li style="margin-bottom: 10px;"><strong style="color: #17a2b8;">Under Review:</strong> Our claims team is currently evaluating your claim.</li>
                        <li style="margin-bottom: 10px;"><strong style="color: #28a745;">Approved:</strong> Your claim has been approved and is being processed for payment.</li>
                        <li style="margin-bottom: 10px;"><strong style="color: #dc3545;">Rejected:</strong> Your claim has been denied. Please check the reason provided.</li>
                        <li><strong style="color: #20c997;">Processed:</strong> Payment for your claim has been processed.</li>
                    </ul>
                    <p style="margin-top: 15px;">If you have any questions about your claim status, please contact our claims department.</p>
                    <a href="contact.php" style="display: inline-block; margin-top: 10px; color: #0056b3;">Contact Claims Department</a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include_once 'includes/footer.php';
?>
