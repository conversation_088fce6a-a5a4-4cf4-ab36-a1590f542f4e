<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Handle payment status updates
if(isset($_POST['update_payment_status'])) {
    $payment_id = $_POST['payment_id'];
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes']);
    
    try {
        $stmt = $conn->prepare("UPDATE payments SET status = :status, notes = :notes, verified_by = :verified_by, verified_at = NOW() WHERE payment_id = :payment_id");
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':verified_by', $_SESSION['user_id']);
        $stmt->bindParam(':payment_id', $payment_id);
        
        if($stmt->execute()) {
            $success_message = "Payment status updated successfully!";
        } else {
            $error_message = "Error updating payment status.";
        }
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all payments with customer and policy info
$query = "SELECT p.*, cp.policy_number, pol.policy_name, pt.type_name,
                 CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
                 customer.email as customer_email,
                 CONCAT(recorder.first_name, ' ', recorder.last_name) as recorded_by_name,
                 CONCAT(verifier.first_name, ' ', verifier.last_name) as verified_by_name
          FROM payments p
          JOIN customer_policies cp ON p.customer_policy_id = cp.customer_policy_id
          JOIN policies pol ON cp.policy_id = pol.policy_id
          JOIN policy_types pt ON pol.policy_type_id = pt.policy_type_id
          JOIN users customer ON cp.customer_id = customer.user_id
          LEFT JOIN users recorder ON p.recorded_by = recorder.user_id
          LEFT JOIN users verifier ON p.verified_by = verifier.user_id
          ORDER BY p.payment_date DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Payments";
$page_description = "View and manage all payment transactions.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #17a2b8; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 12px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .status-refunded { background-color: #d1ecf1; color: #0c5460; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #17a2b8; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .search-box { margin-bottom: 20px; }
        .search-box input, .search-box select { padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #17a2b8; }
        .stat-number { font-size: 24px; font-weight: bold; color: #17a2b8; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Admin Dashboard</a>
        
        <div class="content">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- Payment Statistics -->
            <div class="stats-grid">
                <?php
                $total_payments = count($payments);
                $completed_payments = count(array_filter($payments, function($p) { return $p['status'] == 'Completed'; }));
                $pending_payments = count(array_filter($payments, function($p) { return $p['status'] == 'Pending'; }));
                $total_amount = array_sum(array_column($payments, 'amount'));
                $completed_amount = array_sum(array_column(array_filter($payments, function($p) { return $p['status'] == 'Completed'; }), 'amount'));
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_payments; ?></div>
                    <div class="stat-label">Total Payments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $completed_payments; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $pending_payments; ?></div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSH <?php echo number_format($completed_amount, 0); ?></div>
                    <div class="stat-label">Total Received</div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>All Payments (<?php echo count($payments); ?>)</h2>
            </div>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Search payments..." onkeyup="searchPayments()">
                <select id="statusFilter" onchange="filterByStatus()">
                    <option value="">All Statuses</option>
                    <option value="Pending">Pending</option>
                    <option value="Completed">Completed</option>
                    <option value="Failed">Failed</option>
                    <option value="Refunded">Refunded</option>
                </select>
                <select id="methodFilter" onchange="filterByMethod()">
                    <option value="">All Methods</option>
                    <option value="Credit Card">Credit Card</option>
                    <option value="Debit Card">Debit Card</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Mpesa">Mpesa</option>
                    <option value="Cash">Cash</option>
                </select>
            </div>

            <table class="table" id="paymentsTable">
                <thead>
                    <tr>
                        <th>Payment #</th>
                        <th>Customer</th>
                        <th>Policy</th>
                        <th>Amount</th>
                        <th>Method</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Reference</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($payments as $payment): ?>
                    <tr>
                        <td><strong><?php echo htmlspecialchars($payment['payment_number']); ?></strong></td>
                        <td>
                            <?php echo htmlspecialchars($payment['customer_name']); ?><br>
                            <small style="color: #6c757d;"><?php echo htmlspecialchars($payment['customer_email']); ?></small>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($payment['policy_name']); ?><br>
                            <small style="color: #6c757d;"><?php echo htmlspecialchars($payment['policy_number']); ?></small>
                        </td>
                        <td><strong>KSH <?php echo number_format($payment['amount'], 2); ?></strong></td>
                        <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower($payment['status']); ?>">
                                <?php echo $payment['status']; ?>
                            </span>
                        </td>
                        <td><?php echo date('M d, Y H:i', strtotime($payment['payment_date'])); ?></td>
                        <td><?php echo htmlspecialchars($payment['transaction_reference'] ?: 'N/A'); ?></td>
                        <td>
                            <button onclick="viewPayment(<?php echo $payment['payment_id']; ?>)" class="btn btn-primary">View</button>
                            <?php if($payment['status'] == 'Pending'): ?>
                                <button onclick="verifyPayment(<?php echo $payment['payment_id']; ?>)" class="btn btn-warning">Verify</button>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if(empty($payments)): ?>
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No payments found</h3>
                    <p>Payment transactions will appear here when customers make payments.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Verify Payment Modal -->
    <div id="verifyPaymentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>Verify Payment</h3>
            <form method="post" id="verifyPaymentForm">
                <input type="hidden" name="payment_id" id="modalPaymentId">
                
                <div style="margin-bottom: 15px;">
                    <label>Status:</label>
                    <select name="status" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="Completed">Completed</option>
                        <option value="Failed">Failed</option>
                        <option value="Refunded">Refunded</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label>Verification Notes:</label>
                    <textarea name="notes" rows="4" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="Add notes about the verification..."></textarea>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" name="update_payment_status" class="btn btn-primary">Update Payment</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function searchPayments() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('paymentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length - 1; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                rows[i].style.display = found ? '' : 'none';
            }
        }

        function filterByStatus() {
            const select = document.getElementById('statusFilter');
            const filter = select.value;
            const table = document.getElementById('paymentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const statusCell = rows[i].getElementsByTagName('td')[5];
                if (filter === '' || statusCell.textContent.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }

        function filterByMethod() {
            const select = document.getElementById('methodFilter');
            const filter = select.value;
            const table = document.getElementById('paymentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const methodCell = rows[i].getElementsByTagName('td')[4];
                if (filter === '' || methodCell.textContent.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }

        function viewPayment(paymentId) {
            alert('Payment details view would be implemented here for payment ID: ' + paymentId);
        }

        function verifyPayment(paymentId) {
            document.getElementById('modalPaymentId').value = paymentId;
            document.getElementById('verifyPaymentModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('verifyPaymentModal').style.display = 'none';
        }

        window.onclick = function(event) {
            const modal = document.getElementById('verifyPaymentModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
