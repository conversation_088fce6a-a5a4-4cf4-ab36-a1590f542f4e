<?php
$success = false;
$error_message = "";

if(isset($_POST['import_database'])) {
    try {
        $db_host = "127.0.0.1";
        $db_user = "root";
        $db_password = "";
        
        $conn = new PDO("mysql:host=$db_host", $db_user, $db_password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql_file = file_get_contents('database/zamara_insurance_system.sql');
        
        if($sql_file === false) {
            throw new Exception("Could not read SQL file. Make sure the file exists in the database folder.");
        }
        
        $statements = explode(';', $sql_file);
        
        foreach($statements as $statement) {
            $statement = trim($statement);
            if(!empty($statement) && !preg_match('/^--/', $statement)) {
                $conn->exec($statement);
            }
        }
        
        $success = true;
        
    } catch(Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Zamara Insurance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background: #0056b3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #004494;
        }
        .credentials {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .credential-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #0056b3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Zamara Insurance System</h1>
            <h2>Database Setup</h2>
        </div>

        <?php if($success): ?>
            <div class="success">
                <h3>✅ Database Setup Complete!</h3>
                <p>The Zamara Insurance database has been successfully created with all necessary tables and sample data.</p>
            </div>

            <div class="credentials">
                <h3>Login Credentials</h3>
                <p>You can now log in with any of the following accounts:</p>

                <div class="credential-box">
                    <h4 style="color: #0056b3; margin-bottom: 10px;">👨‍💼 Administrator</h4>
                    <p><strong>Username:</strong> admin</p>
                    <p><strong>Password:</strong> admin123</p>
                    <p><em>Full system access - manage users, policies, claims, and reports</em></p>
                </div>

                <div class="credential-box">
                    <h4 style="color: #28a745; margin-bottom: 10px;">🏢 Insurance Agent</h4>
                    <p><strong>Username:</strong> agent1</p>
                    <p><strong>Password:</strong> agent123</p>
                    <p><em>Register customers, create policies, and process claims</em></p>
                </div>

                <div class="credential-box">
                    <h4 style="color: #ffc107; margin-bottom: 10px;">💰 System Accountant</h4>
                    <p><strong>Username:</strong> accountant1</p>
                    <p><strong>Password:</strong> accountant123</p>
                    <p><em>Manage premium payments and financial records</em></p>
                </div>

                <div class="credential-box">
                    <h4 style="color: #17a2b8; margin-bottom: 10px;">👤 Customer</h4>
                    <p><strong>Username:</strong> customer1</p>
                    <p><strong>Password:</strong> customer123</p>
                    <p><em>Purchase policies, file claims, and track payments</em></p>
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ Security Notice:</strong> Please change all default passwords after your first login for security purposes.
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="login.php" class="btn">Go to Login Page</a>
                <a href="index.php" class="btn" style="margin-left: 10px; background: #28a745;">View Website</a>
            </div>

        <?php elseif($error_message): ?>
            <div class="error">
                <h3>❌ Setup Failed</h3>
                <p><?php echo htmlspecialchars($error_message); ?></p>
            </div>

            <div style="text-align: center;">
                <form method="post">
                    <button type="submit" name="import_database" class="btn">Try Again</button>
                </form>
            </div>

        <?php else: ?>
            <div style="text-align: center;">
                <h3>Ready to Set Up Your Database?</h3>
                <p>This will create the Zamara Insurance database with all necessary tables, sample data, and user accounts.</p>
                
                <div class="warning">
                    <strong>⚠️ Important:</strong> Make sure XAMPP MySQL service is running before proceeding.
                </div>

                <form method="post">
                    <button type="submit" name="import_database" class="btn">Set Up Database</button>
                </form>

                <div style="margin-top: 30px; text-align: left;">
                    <h4>What will be created:</h4>
                    <ul>
                        <li>✅ Complete database structure with all tables</li>
                        <li>✅ Role-based user system (Admin, Agent, Accountant, Customer)</li>
                        <li>✅ 12 sample insurance policies</li>
                        <li>✅ Sample user accounts for testing</li>
                        <li>✅ Proper relationships and constraints</li>
                        <li>✅ Security features and audit trails</li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>&copy; <?php echo date('Y'); ?> Zamara Insurance Management System</p>
        </div>
    </div>
</body>
</html>
