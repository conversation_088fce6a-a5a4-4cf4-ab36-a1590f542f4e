<?php
class Role {
    private $conn;
    private $table_name = "roles";
    
    // Role properties
    public $role_id;
    public $role_name;
    public $description;
    public $created_at;
    public $updated_at;
    
    // Constructor
    public function __construct($db) {
        $this->conn = $db;
    }
    
    // Read all roles
    public function readAll() {
        // Query to read all roles
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY role_name";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Execute query
        $stmt->execute();
        
        return $stmt;
    }
    
    // Read single role
    public function readOne() {
        // Query to read single role
        $query = "SELECT * FROM " . $this->table_name . " WHERE role_id = :role_id LIMIT 0,1";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bindParam(":role_id", $this->role_id);
        
        // Execute query
        $stmt->execute();
        
        // Fetch row
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($row) {
            // Set properties
            $this->role_name = $row['role_name'];
            $this->description = $row['description'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }
        
        return false;
    }
    
    // Create new role
    public function create() {
        // Sanitize inputs
        $this->role_name = htmlspecialchars(strip_tags($this->role_name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        
        // Insert query
        $query = "INSERT INTO " . $this->table_name . "
                SET role_name = :role_name,
                    description = :description";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind values
        $stmt->bindParam(":role_name", $this->role_name);
        $stmt->bindParam(":description", $this->description);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        return false;
    }
    
    // Update role
    public function update() {
        // Sanitize inputs
        $this->role_id = htmlspecialchars(strip_tags($this->role_id));
        $this->role_name = htmlspecialchars(strip_tags($this->role_name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        
        // Update query
        $query = "UPDATE " . $this->table_name . "
                SET role_name = :role_name,
                    description = :description
                WHERE role_id = :role_id";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind values
        $stmt->bindParam(":role_id", $this->role_id);
        $stmt->bindParam(":role_name", $this->role_name);
        $stmt->bindParam(":description", $this->description);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        return false;
    }
    
    // Delete role
    public function delete() {
        // Sanitize ID
        $this->role_id = htmlspecialchars(strip_tags($this->role_id));
        
        // Delete query
        $query = "DELETE FROM " . $this->table_name . " WHERE role_id = :role_id";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bindParam(":role_id", $this->role_id);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        return false;
    }
    
    // Get users by role
    public function getUsersByRole() {
        // Query to get users by role
        $query = "SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.is_active
                FROM users u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE ur.role_id = :role_id
                ORDER BY u.last_name, u.first_name";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bindParam(":role_id", $this->role_id);
        
        // Execute query
        $stmt->execute();
        
        return $stmt;
    }
}
?>
