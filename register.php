<?php
session_start();

if(isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
    header("location: dashboard.php");
    exit;
}

require_once "includes/db_config.php";

$username = $email = $password = $confirm_password = $first_name = $last_name = $phone = "";
$username_err = $email_err = $password_err = $confirm_password_err = $first_name_err = $last_name_err = $phone_err = "";


if($_SERVER["REQUEST_METHOD"] == "POST") {


    if(empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } elseif(!preg_match('/^[a-zA-Z0-9_]+$/', trim($_POST["username"]))) {
        $username_err = "Username can only contain letters, numbers, and underscores.";
    } else {

        $sql = "SELECT user_id FROM users WHERE username = :username";

        if($stmt = $conn->prepare($sql)) {

            $stmt->bindParam(":username", $param_username, PDO::PARAM_STR);


            $param_username = trim($_POST["username"]);


            if($stmt->execute()) {
                if($stmt->rowCount() == 1) {
                    $username_err = "This username is already taken.";
                } else {
                    $username = trim($_POST["username"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }


            unset($stmt);
        }
    }


    if(empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } elseif(!filter_var(trim($_POST["email"]), FILTER_VALIDATE_EMAIL)) {
        $email_err = "Please enter a valid email address.";
    } else {

        $sql = "SELECT user_id FROM users WHERE email = :email";

        if($stmt = $conn->prepare($sql)) {

            $stmt->bindParam(":email", $param_email, PDO::PARAM_STR);


            $param_email = trim($_POST["email"]);


            if($stmt->execute()) {
                if($stmt->rowCount() == 1) {
                    $email_err = "This email is already registered.";
                } else {
                    $email = trim($_POST["email"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }


            unset($stmt);
        }
    }


    if(empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter your first name.";
    } else {
        $first_name = trim($_POST["first_name"]);
    }


    if(empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter your last name.";
    } else {
        $last_name = trim($_POST["last_name"]);
    }


    if(empty(trim($_POST["phone"]))) {
        $phone_err = "Please enter your phone number.";
    } else {
        $phone = trim($_POST["phone"]);
    }


    if(empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";
    } elseif(strlen(trim($_POST["password"])) < 6) {
        $password_err = "Password must have at least 6 characters.";
    } else {
        $password = trim($_POST["password"]);
    }


    if(empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "Please confirm password.";
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if(empty($password_err) && ($password != $confirm_password)) {
            $confirm_password_err = "Password did not match.";
        }
    }


    if(empty($username_err) && empty($email_err) && empty($password_err) && empty($confirm_password_err) && empty($first_name_err) && empty($last_name_err) && empty($phone_err)) {

        try {

            $conn->beginTransaction();


            $sql = "INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES (:username, :email, :password, :first_name, :last_name, :phone)";

            if($stmt = $conn->prepare($sql)) {

                $stmt->bindParam(":username", $param_username, PDO::PARAM_STR);
                $stmt->bindParam(":email", $param_email, PDO::PARAM_STR);
                $stmt->bindParam(":password", $param_password, PDO::PARAM_STR);
                $stmt->bindParam(":first_name", $param_first_name, PDO::PARAM_STR);
                $stmt->bindParam(":last_name", $param_last_name, PDO::PARAM_STR);
                $stmt->bindParam(":phone", $param_phone, PDO::PARAM_STR);


                $param_username = $username;
                $param_email = $email;
                $param_password = password_hash($password, PASSWORD_DEFAULT);
                $param_first_name = $first_name;
                $param_last_name = $last_name;
                $param_phone = $phone;


                $stmt->execute();


                $user_id = $conn->lastInsertId();


                $sql = "SELECT role_id FROM roles WHERE role_name = 'Customer'";
                $stmt = $conn->prepare($sql);
                $stmt->execute();

                if($stmt->rowCount() > 0) {
                    $role = $stmt->fetch(PDO::FETCH_ASSOC);
                    $role_id = $role['role_id'];

                    $sql = "INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bindParam(":user_id", $user_id, PDO::PARAM_INT);
                    $stmt->bindParam(":role_id", $role_id, PDO::PARAM_INT);
                    $stmt->execute();
                }


                $conn->commit();


                header("location: login.php");
            }
        } catch(PDOException $e) {

            $conn->rollback();
            echo "Error: " . $e->getMessage();
        }


        unset($stmt);
    }


    unset($conn);
}

$page_title = "Register";
$page_description = "Create a new account with Zamara Insurance to access our services.";

include_once 'includes/header.php';
?>

<section style="padding: 80px 0;">
    <div class="container">
        <div style="max-width: 700px; margin: 0 auto; background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);">
            <h2 style="text-align: center; margin-bottom: 30px; color: #0056b3;">Create an Account</h2>

            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div style="display: flex; flex-wrap: wrap; margin: 0 -10px;">
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">First Name *</label>
                            <input type="text" name="first_name" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $first_name; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $first_name_err; ?></span>
                        </div>
                    </div>
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Last Name *</label>
                            <input type="text" name="last_name" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $last_name; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $last_name_err; ?></span>
                        </div>
                    </div>
                </div>

                <div style="display: flex; flex-wrap: wrap; margin: 0 -10px;">
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Username *</label>
                            <input type="text" name="username" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $username; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $username_err; ?></span>
                        </div>
                    </div>
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Email *</label>
                            <input type="email" name="email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $email; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $email_err; ?></span>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Phone Number *</label>
                    <input type="text" name="phone" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $phone; ?>">
                    <span style="color: #dc3545; font-size: 14px;"><?php echo $phone_err; ?></span>
                </div>

                <div style="display: flex; flex-wrap: wrap; margin: 0 -10px;">
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Password *</label>
                            <input type="password" name="password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $password_err; ?></span>
                        </div>
                    </div>
                    <div style="flex: 0 0 50%; padding: 0 10px; box-sizing: border-box;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">Confirm Password *</label>
                            <input type="password" name="confirm_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $confirm_password_err; ?></span>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <button type="submit" style="width: 100%; padding: 12px; background-color: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Register</button>
                </div>
                <p style="text-align: center;">Already have an account? <a href="login.php" style="color: #0056b3; text-decoration: none;">Login here</a>.</p>
            </form>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>