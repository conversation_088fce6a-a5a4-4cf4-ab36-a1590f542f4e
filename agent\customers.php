<?php
session_start();

// Check if user is logged in and is agent
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Insurance Agent") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = $error_message = "";

// Get all customers with their policies
$query = "SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.phone, u.status, u.created_at,
                 COUNT(cp.customer_policy_id) as policy_count,
                 SUM(CASE WHEN cp.status = 'Active' THEN 1 ELSE 0 END) as active_policies
          FROM users u
          JOIN user_roles ur ON u.user_id = ur.user_id
          JOIN roles r ON ur.role_id = r.role_id
          LEFT JOIN customer_policies cp ON u.user_id = cp.customer_id
          WHERE r.role_name = 'Customer'
          GROUP BY u.user_id
          ORDER BY u.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Customers";
$page_description = "View and manage customer accounts and policies.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px 0; margin-bottom: 30px; }
        .content { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; font-size: 12px; }
        .btn-primary { background-color: #0056b3; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
        .status-suspended { background-color: #fff3cd; color: #856404; }
        .back-link { display: inline-block; margin-bottom: 20px; color: #28a745; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .search-box { margin-bottom: 20px; }
        .search-box input { padding: 10px; border: 1px solid #ddd; border-radius: 4px; width: 300px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #28a745; }
        .stat-number { font-size: 24px; font-weight: bold; color: #28a745; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
        .customer-card { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><?php echo $page_title; ?></h1>
            <p><?php echo $page_description; ?></p>
        </div>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← Back to Agent Dashboard</a>

        <div class="content">
            <?php if(!empty($success_message)): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(!empty($error_message)): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- Customer Statistics -->
            <div class="stats-grid">
                <?php
                $total_customers = count($customers);
                $active_customers = count(array_filter($customers, function($c) { return $c['status'] == 'Active'; }));
                $customers_with_policies = count(array_filter($customers, function($c) { return $c['policy_count'] > 0; }));
                $total_policies = array_sum(array_column($customers, 'policy_count'));
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_customers; ?></div>
                    <div class="stat-label">Total Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $active_customers; ?></div>
                    <div class="stat-label">Active Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $customers_with_policies; ?></div>
                    <div class="stat-label">With Policies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_policies; ?></div>
                    <div class="stat-label">Total Policies</div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>All Customers (<?php echo count($customers); ?>)</h2>
                <a href="add_customer.php" class="btn btn-success">Add New Customer</a>
            </div>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Search customers..." onkeyup="searchCustomers()">
            </div>

            <table class="table" id="customersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Policies</th>
                        <th>Active Policies</th>
                        <th>Status</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($customers as $customer): ?>
                    <tr>
                        <td><?php echo $customer['user_id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></strong><br>
                            <small style="color: #6c757d;"><?php echo htmlspecialchars($customer['username']); ?></small>
                        </td>
                        <td><?php echo htmlspecialchars($customer['email']); ?></td>
                        <td><?php echo htmlspecialchars($customer['phone'] ?: 'N/A'); ?></td>
                        <td><strong><?php echo $customer['policy_count']; ?></strong></td>
                        <td><strong><?php echo $customer['active_policies']; ?></strong></td>
                        <td>
                            <span class="status-badge status-<?php echo strtolower($customer['status']); ?>">
                                <?php echo $customer['status']; ?>
                            </span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($customer['created_at'])); ?></td>
                        <td>
                            <a href="view_customer.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-primary">View</a>
                            <a href="customer_policies.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-info">Policies</a>
                            <a href="assign_policy.php?customer_id=<?php echo $customer['user_id']; ?>" class="btn btn-success">Assign Policy</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if(empty($customers)): ?>
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <h3>No customers found</h3>
                    <p>Customer accounts will appear here when they register.</p>
                </div>
            <?php endif; ?>

            <!-- Customer Cards View (Alternative) -->
            <div style="margin-top: 40px;">
                <h3>Customer Overview</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                    <?php foreach(array_slice($customers, 0, 6) as $customer): ?>
                    <div class="customer-card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                            <div style="flex: 1;">
                                <h4 style="color: #28a745; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?>
                                </h4>
                                <p style="color: #6c757d; font-size: 14px; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($customer['email']); ?>
                                </p>
                                <p style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">
                                    <?php echo htmlspecialchars($customer['phone'] ?: 'No phone'); ?>
                                </p>
                                <div style="display: flex; gap: 10px;">
                                    <span class="status-badge status-<?php echo strtolower($customer['status']); ?>">
                                        <?php echo $customer['status']; ?>
                                    </span>
                                    <span style="background-color: #e9ecef; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                        <?php echo $customer['policy_count']; ?> policies
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 5px; margin-top: 10px;">
                            <a href="view_customer.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-primary" style="flex: 1; text-align: center;">View</a>
                            <a href="customer_policies.php?id=<?php echo $customer['user_id']; ?>" class="btn btn-info" style="flex: 1; text-align: center;">Policies</a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function searchCustomers() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('customersTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length - 1; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }

                rows[i].style.display = found ? '' : 'none';
            }
        }
    </script>
</body>
</html>
