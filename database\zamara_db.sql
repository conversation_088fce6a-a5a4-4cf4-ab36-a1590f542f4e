-- Zamara Insurance Management System Database Schema

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS zamara_db;
USE zamara_db;

-- Drop tables if they exist to avoid conflicts
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS claims;
DROP TABLE IF EXISTS customer_policies;
DROP TABLE IF EXISTS policies;
DROP TABLE IF EXISTS policy_types;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS users;

-- Create Roles Table
CREATE TABLE roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Create Users Table
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    date_of_birth DATE,
    profile_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Create User_Roles Table (Many-to-Many relationship)
CREATE TABLE user_roles (
    user_role_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_id)
) ENGINE=InnoDB;

-- Create Policy_Types Table
CREATE TABLE policy_types (
    policy_type_id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Create Policies Table
CREATE TABLE policies (
    policy_id INT AUTO_INCREMENT PRIMARY KEY,
    policy_name VARCHAR(100) NOT NULL,
    policy_type_id INT NOT NULL,
    description TEXT,
    duration VARCHAR(50) NOT NULL,
    premium_amount DECIMAL(10, 2) NOT NULL,
    coverage_amount DECIMAL(10, 2) NOT NULL,
    terms_conditions TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (policy_type_id) REFERENCES policy_types(policy_type_id),
    FOREIGN KEY (created_by) REFERENCES users(user_id)
) ENGINE=InnoDB;

-- Create Customer_Policies Table
CREATE TABLE customer_policies (
    customer_policy_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    policy_id INT NOT NULL,
    policy_number VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('Active', 'Expired', 'Cancelled', 'Pending') DEFAULT 'Pending',
    total_premium DECIMAL(10, 2) NOT NULL,
    payment_frequency ENUM('Monthly', 'Quarterly', 'Semi-Annually', 'Annually') DEFAULT 'Monthly',
    next_payment_date DATE,
    agent_id INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(policy_id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES users(user_id)
) ENGINE=InnoDB;

-- Create Claims Table
CREATE TABLE claims (
    claim_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_policy_id INT NOT NULL,
    claim_number VARCHAR(50) NOT NULL UNIQUE,
    claim_date DATE NOT NULL,
    incident_date DATE NOT NULL,
    description TEXT NOT NULL,
    claim_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('Submitted', 'Under Review', 'Approved', 'Rejected', 'Paid') DEFAULT 'Submitted',
    documents TEXT,
    reviewed_by INT,
    review_notes TEXT,
    review_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_policy_id) REFERENCES customer_policies(customer_policy_id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(user_id)
) ENGINE=InnoDB;

-- Create Payments Table
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_policy_id INT NOT NULL,
    payment_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method ENUM('Credit Card', 'Debit Card', 'Bank Transfer', 'Cash', 'Mobile Money') NOT NULL,
    transaction_id VARCHAR(100),
    status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
    notes TEXT,
    recorded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_policy_id) REFERENCES customer_policies(customer_policy_id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(user_id)
) ENGINE=InnoDB;

-- Insert default roles
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Manages the entire system, including users and policies'),
('Insurance Agent', 'Registers customers, creates policies, and processes claims'),
('Customer', 'Can register, purchase policies, file claims, and track payments'),
('System Accountant', 'Manages premium payments and financial records');

-- Insert default policy types
INSERT INTO policy_types (type_name, description) VALUES
('Life Insurance', 'Provides financial protection to beneficiaries upon the death of the insured'),
('Health Insurance', 'Covers medical expenses for illness or injury'),
('Property Insurance', 'Protects against damage or loss of property'),
('Auto Insurance', 'Provides financial protection against physical damage and bodily injury resulting from traffic accidents'),
('Travel Insurance', 'Covers unexpected events during travel');

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES
('admin', '<EMAIL>', '$2y$10$Ot5BKiGFcmj5Wf7Zqq3Xf.LQU.KLZRsYJQBIQtGxnIwbzIxW5Ry0G', 'System', 'Administrator', '+************');

-- Insert agent user (password: agent123)
INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES
('agent', '<EMAIL>', '$2y$10$Ot5BKiGFcmj5Wf7Zqq3Xf.LQU.KLZRsYJQBIQtGxnIwbzIxW5Ry0G', 'Insurance', 'Agent', '+************');

-- Insert accountant user (password: accountant123)
INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES
('accountant', '<EMAIL>', '$2y$10$Ot5BKiGFcmj5Wf7Zqq3Xf.LQU.KLZRsYJQBIQtGxnIwbzIxW5Ry0G', 'System', 'Accountant', '+************');

-- Insert customer user (password: customer123)
INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES
('customer', '<EMAIL>', '$2y$10$Ot5BKiGFcmj5Wf7Zqq3Xf.LQU.KLZRsYJQBIQtGxnIwbzIxW5Ry0G', 'John', 'Doe', '+************');

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1), -- admin as Administrator
(2, 2), -- agent as Insurance Agent
(3, 4), -- accountant as System Accountant
(4, 3); -- customer as Customer
